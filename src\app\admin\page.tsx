import { Metadata } from 'next';
import { SessionPoolDashboard } from '@/components/SessionPoolDashboard';
import { ErrorBoundary } from '@/components/ErrorBoundary';

export const metadata: Metadata = {
  title: 'Admin Dashboard | Instagram Story Viewer',
  description: 'Administrative dashboard for monitoring session pool and account health',
  robots: {
    index: false,
    follow: false,
  },
};

export default function AdminPage() {
  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              Admin Dashboard
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              Monitor and manage the Intelligent Session Management System
            </p>
          </div>
          
          <SessionPoolDashboard />
        </div>
      </div>
    </ErrorBoundary>
  );
}
