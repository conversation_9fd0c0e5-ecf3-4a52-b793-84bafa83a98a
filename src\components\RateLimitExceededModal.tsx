'use client';

import React, { useState, useEffect } from 'react';
import { X, Clock, AlertTriangle, Crown, Shield, Zap } from 'lucide-react';
import { UserTier } from '@/lib/types/rate-limiting';

interface RateLimitExceededModalProps {
  isOpen: boolean;
  onClose: () => void;
  resetTime: number;
  userTier: UserTier;
  dailyLimit: number;
  onUpgrade?: () => void;
  reason?: string;
}

export function RateLimitExceededModal({
  isOpen,
  onClose,
  resetTime,
  userTier,
  dailyLimit,
  onUpgrade,
  reason
}: RateLimitExceededModalProps) {
  const [timeUntilReset, setTimeUntilReset] = useState<string>('');
  const [showComingSoonTooltip, setShowComingSoonTooltip] = useState(false);

  useEffect(() => {
    if (!isOpen || !resetTime) return;

    const updateCountdown = () => {
      const now = Date.now();
      const timeLeft = resetTime - now;

      if (timeLeft <= 0) {
        setTimeUntilReset('Available now');
        return;
      }

      const hours = Math.floor(timeLeft / (1000 * 60 * 60));
      const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

      if (hours > 0) {
        setTimeUntilReset(`${hours} hour${hours > 1 ? 's' : ''} and ${minutes} minute${minutes > 1 ? 's' : ''}`);
      } else if (minutes > 0) {
        setTimeUntilReset(`${minutes} minute${minutes > 1 ? 's' : ''} and ${seconds} second${seconds > 1 ? 's' : ''}`);
      } else {
        setTimeUntilReset(`${seconds} second${seconds > 1 ? 's' : ''}`);
      }
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, [isOpen, resetTime]);

  if (!isOpen) return null;

  const getTierInfo = (tier: UserTier) => {
    switch (tier) {
      case UserTier.PREMIUM:
        return {
          name: 'Premium',
          icon: <Crown className="h-6 w-6 text-yellow-500" />,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
          borderColor: 'border-yellow-200 dark:border-yellow-800'
        };
      case UserTier.ENTERPRISE:
        return {
          name: 'Enterprise',
          icon: <Crown className="h-6 w-6 text-purple-500" />,
          color: 'text-purple-600',
          bgColor: 'bg-purple-50 dark:bg-purple-900/20',
          borderColor: 'border-purple-200 dark:border-purple-800'
        };
      default:
        return {
          name: 'Free',
          icon: <Shield className="h-6 w-6 text-blue-500" />,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50 dark:bg-blue-900/20',
          borderColor: 'border-blue-200 dark:border-blue-800'
        };
    }
  };

  const tierInfo = getTierInfo(userTier);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <AlertTriangle className="h-6 w-6 text-red-500" />
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Rate Limit Exceeded
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          {/* Current Tier Status */}
          <div className={`p-4 rounded-lg border ${tierInfo.bgColor} ${tierInfo.borderColor}`}>
            <div className="flex items-center space-x-3">
              {tierInfo.icon}
              <div>
                <h3 className={`font-medium ${tierInfo.color}`}>
                  {tierInfo.name} Plan
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {dailyLimit} requests per day
                </p>
              </div>
            </div>
          </div>

          {/* Main Message */}
          <div className="text-center">
            <p className="text-gray-700 dark:text-gray-300 mb-2">
              You've reached your daily limit of <strong>{dailyLimit} requests</strong>.
            </p>
            {reason && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                {reason}
              </p>
            )}
          </div>

          {/* Reset Timer */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-center space-x-2">
              <Clock className="h-5 w-5 text-gray-500" />
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Your limit resets in:
              </span>
            </div>
            <div className="text-center mt-2">
              <span className="text-xl font-mono font-bold text-gray-900 dark:text-white">
                {timeUntilReset}
              </span>
            </div>
          </div>

          {/* Upgrade Options */}
          {userTier === UserTier.FREE && onUpgrade && (
            <div className="space-y-3">
              <div className="text-center">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Need more requests? Upgrade your plan:
                </p>
              </div>

              <div className="grid gap-3">
                {/* Premium Option */}
                <div className="border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 bg-yellow-50 dark:bg-yellow-900/20">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Crown className="h-5 w-5 text-yellow-500" />
                      <div>
                        <h4 className="font-medium text-yellow-800 dark:text-yellow-200">
                          Premium Plan
                        </h4>
                        <p className="text-sm text-yellow-600 dark:text-yellow-300">
                          50 requests per day
                        </p>
                      </div>
                    </div>
                    <div className="relative">
                      <button
                        onClick={() => {
                          setShowComingSoonTooltip(true);
                          setTimeout(() => setShowComingSoonTooltip(false), 3000);
                        }}
                        className="px-4 py-2 bg-gray-400 text-gray-600 text-sm rounded-md cursor-not-allowed opacity-75 flex items-center space-x-1"
                        title="Coming Soon"
                      >
                        <Zap className="h-4 w-4" />
                        <span>Coming Soon</span>
                      </button>

                      {/* Coming Soon Tooltip */}
                      {showComingSoonTooltip && (
                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded shadow-lg z-10 whitespace-nowrap">
                          Upgrade feature coming soon!
                          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-900"></div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Enterprise Option */}
                <div className="border border-purple-200 dark:border-purple-800 rounded-lg p-4 bg-purple-50 dark:bg-purple-900/20">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Crown className="h-5 w-5 text-purple-500" />
                      <div>
                        <h4 className="font-medium text-purple-800 dark:text-purple-200">
                          Enterprise Plan
                        </h4>
                        <p className="text-sm text-purple-600 dark:text-purple-300">
                          1,000 requests per day
                        </p>
                      </div>
                    </div>
                    <div className="relative">
                      <button
                        onClick={() => {
                          setShowComingSoonTooltip(true);
                          setTimeout(() => setShowComingSoonTooltip(false), 3000);
                        }}
                        className="px-4 py-2 bg-gray-400 text-gray-600 text-sm rounded-md cursor-not-allowed opacity-75 flex items-center space-x-1"
                        title="Coming Soon"
                      >
                        <Zap className="h-4 w-4" />
                        <span>Coming Soon</span>
                      </button>

                      {/* Coming Soon Tooltip */}
                      {showComingSoonTooltip && (
                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded shadow-lg z-10 whitespace-nowrap">
                          Upgrade feature coming soon!
                          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-900"></div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Alternative Options */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
              What you can do:
            </h4>
            <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
              <li>• Wait for your limit to reset automatically</li>
              <li>• Upgrade to a higher plan for more requests</li>
              <li>• Bookmark this page and return later</li>
            </ul>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
          >
            Close
          </button>
          {timeUntilReset === 'Available now' && (
            <button
              onClick={() => {
                onClose();
                window.location.reload();
              }}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

export default RateLimitExceededModal;
