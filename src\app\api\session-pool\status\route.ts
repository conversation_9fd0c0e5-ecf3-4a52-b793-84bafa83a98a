import { NextRequest, NextResponse } from 'next/server';
import { IntelligentSessionManager } from '@/lib/services/intelligent-session-manager';

// Global session manager instance
let sessionManagerInstance: IntelligentSessionManager | null = null;

function getSessionManager(): IntelligentSessionManager {
  if (!sessionManagerInstance) {
    sessionManagerInstance = new IntelligentSessionManager();
  }
  return sessionManagerInstance;
}

export async function GET(request: NextRequest) {
  try {
    const sessionManager = getSessionManager();
    
    // Try to get status, initialize if needed
    let status;
    try {
      status = await sessionManager.getDetailedStatus();
    } catch (error) {
      // If not initialized, try to initialize
      if (error instanceof Error && error.message.includes('not initialized')) {
        console.log('🚀 Session manager not initialized, initializing now...');
        try {
          await sessionManager.initialize();
          status = await sessionManager.getDetailedStatus();
        } catch (initError) {
          console.error('❌ Failed to initialize session manager:', initError);
          return NextResponse.json({
            initialized: false,
            error: initError instanceof Error ? initError.message : 'Initialization failed',
            activeSessions: [],
            accountSummary: { 
              total: 0, 
              healthy: 0, 
              inCooldown: 0, 
              withErrors: 0 
            },
            metrics: {
              activeSessions: 0,
              healthyAccounts: 0,
              totalAccounts: 0,
              averageSessionAge: 0,
              requestsPerHour: 0,
              rotationsToday: 0,
              errorRate: 0
            },
            timestamp: new Date().toISOString()
          });
        }
      } else {
        throw error;
      }
    }
    
    // Add timestamp to response
    const response = {
      ...status,
      timestamp: new Date().toISOString()
    };
    
    return NextResponse.json(response);
    
  } catch (error) {
    console.error('Session pool status API error:', error);
    
    return NextResponse.json({
      initialized: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      activeSessions: [],
      accountSummary: { 
        total: 0, 
        healthy: 0, 
        inCooldown: 0, 
        withErrors: 0 
      },
      metrics: {
        activeSessions: 0,
        healthyAccounts: 0,
        totalAccounts: 0,
        averageSessionAge: 0,
        requestsPerHour: 0,
        rotationsToday: 0,
        errorRate: 0
      },
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// Allow CORS for development
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
