// Advanced Rate Limiting Strategy for Instagram Account Pool
// Comprehensive rate limiting with account-specific limits and intelligent cooling-off periods

import { SmartAccountPoolManager, ManagedAccount } from './smart-account-pool-manager';
import { RateLimitService } from './rate-limit-service';

export interface AccountRateLimit {
  accountId: string;
  requestsPerHour: number;
  requestsPerDay: number;
  requestsPerWeek: number;
  burstLimit: number; // Max requests in 5 minutes
  cooldownMultiplier: number; // Multiplier for base cooldown
  lastRequestTime: number;
  requestHistory: RateLimitEntry[];
}

export interface RateLimitEntry {
  timestamp: number;
  success: boolean;
  responseTime: number;
  targetUsername?: string;
  errorType?: string;
}

export interface RateLimitConfig {
  globalLimits: {
    requestsPerSecond: number;
    requestsPerMinute: number;
    requestsPerHour: number;
    maxConcurrentRequests: number;
  };
  accountLimits: {
    defaultRequestsPerHour: number;
    defaultRequestsPerDay: number;
    defaultBurstLimit: number;
    premiumMultiplier: number; // Multiplier for premium accounts
  };
  cooldownSettings: {
    baseCooldown: number; // Base cooldown in ms
    maxCooldown: number; // Maximum cooldown in ms
    failureMultiplier: number; // Multiplier for failed requests
    successReduction: number; // Reduction factor for successful requests
  };
  adaptiveSettings: {
    enabled: boolean;
    learningRate: number; // How quickly to adapt (0-1)
    riskThreshold: number; // Risk score threshold for adaptive changes
    adaptationInterval: number; // How often to adapt (ms)
  };
}

export interface RateLimitResult {
  allowed: boolean;
  accountId?: string;
  delay: number; // Recommended delay before next request
  reason?: string;
  retryAfter?: number;
  riskScore: number;
  adaptiveAdjustments?: string[];
}

export class AdvancedRateLimiter {
  private accountPool: SmartAccountPoolManager;
  private userRateLimiter: RateLimitService;
  private accountLimits: Map<string, AccountRateLimit> = new Map();
  private globalRequestCount = 0;
  private lastGlobalReset = Date.now();
  private config: RateLimitConfig;
  private adaptationInterval: NodeJS.Timeout | null = null;

  constructor(
    accountPool: SmartAccountPoolManager,
    userRateLimiter: RateLimitService,
    config?: Partial<RateLimitConfig>
  ) {
    this.accountPool = accountPool;
    this.userRateLimiter = userRateLimiter;
    this.config = {
      globalLimits: {
        requestsPerSecond: 10,
        requestsPerMinute: 300,
        requestsPerHour: 5000,
        maxConcurrentRequests: 50
      },
      accountLimits: {
        defaultRequestsPerHour: 20,
        defaultRequestsPerDay: 100,
        defaultBurstLimit: 5,
        premiumMultiplier: 1.5
      },
      cooldownSettings: {
        baseCooldown: 30000, // 30 seconds
        maxCooldown: 600000, // 10 minutes
        failureMultiplier: 2.0,
        successReduction: 0.9
      },
      adaptiveSettings: {
        enabled: true,
        learningRate: 0.1,
        riskThreshold: 70,
        adaptationInterval: 300000 // 5 minutes
      },
      ...config
    };

    this.initializeAccountLimits();
    if (this.config.adaptiveSettings.enabled) {
      this.startAdaptiveAdjustments();
    }
  }

  private initializeAccountLimits() {
    for (const account of this.accountPool.getAllAccounts()) {
      this.createAccountRateLimit(account);
    }
  }

  private createAccountRateLimit(account: ManagedAccount): AccountRateLimit {
    const isPremium = account.credentials.priority && account.credentials.priority > 7;
    const multiplier = isPremium ? this.config.accountLimits.premiumMultiplier : 1;

    const rateLimit: AccountRateLimit = {
      accountId: account.id,
      requestsPerHour: Math.floor(this.config.accountLimits.defaultRequestsPerHour * multiplier),
      requestsPerDay: Math.floor(this.config.accountLimits.defaultRequestsPerDay * multiplier),
      requestsPerWeek: Math.floor(this.config.accountLimits.defaultRequestsPerDay * 7 * multiplier),
      burstLimit: Math.floor(this.config.accountLimits.defaultBurstLimit * multiplier),
      cooldownMultiplier: isPremium ? 0.8 : 1.0,
      lastRequestTime: 0,
      requestHistory: []
    };

    this.accountLimits.set(account.id, rateLimit);
    return rateLimit;
  }

  /**
   * Check if a request is allowed for a specific account
   */
  async checkAccountRateLimit(
    accountId: string,
    targetUsername: string,
    userFingerprint?: string
  ): Promise<RateLimitResult> {
    const account = this.accountPool.getAccount(accountId);
    if (!account) {
      return {
        allowed: false,
        reason: 'Account not found',
        delay: 0,
        riskScore: 100
      };
    }

    // Check user rate limits first
    if (userFingerprint) {
      try {
        const userRateLimit = await this.userRateLimiter.checkRateLimit(
          userFingerprint,
          '127.0.0.1', // Placeholder IP
          '/api/stories',
          'Mozilla/5.0...' // Placeholder user agent
        );

        if (!userRateLimit.allowed) {
          return {
            allowed: false,
            reason: 'User rate limit exceeded',
            delay: 0,
            retryAfter: userRateLimit.resetTime,
            riskScore: 50
          };
        }
      } catch (error) {
        // User rate limit check failed, but continue with account check
        console.warn('User rate limit check failed:', error);
      }
    }

    // Check global limits
    const globalCheck = this.checkGlobalLimits();
    if (!globalCheck.allowed) {
      return globalCheck;
    }

    // Get or create account rate limit
    let accountLimit = this.accountLimits.get(accountId);
    if (!accountLimit) {
      accountLimit = this.createAccountRateLimit(account);
    }

    // Check account-specific limits
    const accountCheck = this.checkAccountLimits(account, accountLimit, targetUsername);
    if (!accountCheck.allowed) {
      return accountCheck;
    }

    // Calculate risk score and adaptive adjustments
    const riskScore = this.calculateRiskScore(account, accountLimit, targetUsername);
    const adaptiveAdjustments = this.getAdaptiveAdjustments(account, riskScore);

    // Calculate recommended delay
    const delay = this.calculateOptimalDelay(account, accountLimit, riskScore);

    return {
      allowed: true,
      accountId,
      delay,
      riskScore,
      adaptiveAdjustments
    };
  }

  private checkGlobalLimits(): RateLimitResult {
    const now = Date.now();
    const timeSinceReset = now - this.lastGlobalReset;

    // Reset global counter every hour
    if (timeSinceReset > 60 * 60 * 1000) {
      this.globalRequestCount = 0;
      this.lastGlobalReset = now;
    }

    if (this.globalRequestCount >= this.config.globalLimits.requestsPerHour) {
      return {
        allowed: false,
        reason: 'Global hourly limit exceeded',
        delay: 0,
        retryAfter: this.lastGlobalReset + (60 * 60 * 1000),
        riskScore: 30
      };
    }

    return { allowed: true, delay: 0, riskScore: 0 };
  }

  private checkAccountLimits(
    account: ManagedAccount,
    accountLimit: AccountRateLimit,
    targetUsername: string
  ): RateLimitResult {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    const oneDay = 24 * oneHour;
    const fiveMinutes = 5 * 60 * 1000;

    // Clean old entries
    accountLimit.requestHistory = accountLimit.requestHistory.filter(
      entry => now - entry.timestamp < oneDay
    );

    // Check hourly limit
    const hourlyRequests = accountLimit.requestHistory.filter(
      entry => now - entry.timestamp < oneHour
    ).length;

    if (hourlyRequests >= accountLimit.requestsPerHour) {
      return {
        allowed: false,
        accountId: account.id,
        reason: 'Account hourly limit exceeded',
        delay: 0,
        retryAfter: now + oneHour,
        riskScore: 60
      };
    }

    // Check daily limit
    const dailyRequests = accountLimit.requestHistory.length;
    if (dailyRequests >= accountLimit.requestsPerDay) {
      return {
        allowed: false,
        accountId: account.id,
        reason: 'Account daily limit exceeded',
        delay: 0,
        retryAfter: now + oneDay,
        riskScore: 70
      };
    }

    // Check burst limit
    const burstRequests = accountLimit.requestHistory.filter(
      entry => now - entry.timestamp < fiveMinutes
    ).length;

    if (burstRequests >= accountLimit.burstLimit) {
      return {
        allowed: false,
        accountId: account.id,
        reason: 'Account burst limit exceeded',
        delay: fiveMinutes,
        riskScore: 80
      };
    }

    // Check minimum time between requests
    const timeSinceLastRequest = now - accountLimit.lastRequestTime;
    const minInterval = this.calculateMinInterval(account, accountLimit);

    if (timeSinceLastRequest < minInterval) {
      return {
        allowed: false,
        accountId: account.id,
        reason: 'Minimum interval not met',
        delay: minInterval - timeSinceLastRequest,
        riskScore: 40
      };
    }

    return { allowed: true, delay: 0, riskScore: 0 };
  }

  private calculateMinInterval(account: ManagedAccount, accountLimit: AccountRateLimit): number {
    const baseInterval = this.config.cooldownSettings.baseCooldown;
    
    // Adjust based on account health
    let multiplier = accountLimit.cooldownMultiplier;
    
    if (account.health.score < 70) {
      multiplier *= 1.5; // Longer intervals for unhealthy accounts
    }
    
    if (account.health.consecutiveFailures > 0) {
      multiplier *= Math.pow(this.config.cooldownSettings.failureMultiplier, account.health.consecutiveFailures);
    }
    
    if (account.health.consecutiveSuccesses > 5) {
      multiplier *= Math.pow(this.config.cooldownSettings.successReduction, Math.min(account.health.consecutiveSuccesses - 5, 10));
    }
    
    return Math.min(baseInterval * multiplier, this.config.cooldownSettings.maxCooldown);
  }

  private calculateRiskScore(
    account: ManagedAccount,
    accountLimit: AccountRateLimit,
    targetUsername: string
  ): number {
    let riskScore = 0;
    
    // Account health risk
    riskScore += (100 - account.health.score) * 0.3;
    
    // Usage pattern risk
    const hourlyUsage = accountLimit.requestHistory.filter(
      entry => Date.now() - entry.timestamp < 60 * 60 * 1000
    ).length;
    const hourlyRisk = (hourlyUsage / accountLimit.requestsPerHour) * 30;
    riskScore += hourlyRisk;
    
    // Failure rate risk
    const recentFailures = accountLimit.requestHistory.filter(
      entry => Date.now() - entry.timestamp < 60 * 60 * 1000 && !entry.success
    ).length;
    const failureRate = hourlyUsage > 0 ? (recentFailures / hourlyUsage) * 100 : 0;
    riskScore += failureRate * 0.4;
    
    // Time-based risk
    const hour = new Date().getHours();
    if (hour >= 0 && hour <= 6) {
      riskScore += 15; // Higher risk during night hours
    }
    
    // Target repetition risk
    const targetRequests = accountLimit.requestHistory.filter(
      entry => entry.targetUsername === targetUsername && Date.now() - entry.timestamp < 24 * 60 * 60 * 1000
    ).length;
    if (targetRequests > 3) {
      riskScore += (targetRequests - 3) * 5;
    }
    
    return Math.min(100, Math.max(0, riskScore));
  }

  private getAdaptiveAdjustments(account: ManagedAccount, riskScore: number): string[] {
    const adjustments: string[] = [];
    
    if (!this.config.adaptiveSettings.enabled) {
      return adjustments;
    }
    
    if (riskScore > this.config.adaptiveSettings.riskThreshold) {
      adjustments.push('Increased cooldown due to high risk');
      adjustments.push('Enhanced monitoring enabled');
    }
    
    if (account.health.score < 60) {
      adjustments.push('Reduced request frequency for account health');
    }
    
    if (account.health.consecutiveFailures > 2) {
      adjustments.push('Extended cooldown due to recent failures');
    }
    
    return adjustments;
  }

  private calculateOptimalDelay(
    account: ManagedAccount,
    accountLimit: AccountRateLimit,
    riskScore: number
  ): number {
    const baseDelay = this.calculateMinInterval(account, accountLimit);
    
    // Add randomness to mimic human behavior
    const randomFactor = 0.8 + Math.random() * 0.4; // ±20% variation
    let delay = baseDelay * randomFactor;
    
    // Adjust based on risk score
    if (riskScore > 70) {
      delay *= 1.5;
    } else if (riskScore > 50) {
      delay *= 1.2;
    }
    
    // Add some natural variation
    delay += Math.random() * 5000; // Up to 5 seconds additional random delay
    
    return Math.round(delay);
  }

  /**
   * Record a request attempt
   */
  recordRequest(
    accountId: string,
    success: boolean,
    responseTime: number,
    targetUsername?: string,
    errorType?: string
  ) {
    const accountLimit = this.accountLimits.get(accountId);
    if (!accountLimit) return;
    
    const entry: RateLimitEntry = {
      timestamp: Date.now(),
      success,
      responseTime,
      targetUsername,
      errorType
    };
    
    accountLimit.requestHistory.push(entry);
    accountLimit.lastRequestTime = entry.timestamp;
    
    // Update global counter
    this.globalRequestCount++;
    
    // Adaptive learning
    if (this.config.adaptiveSettings.enabled) {
      this.adaptToRequestOutcome(accountId, success, errorType);
    }
  }

  private adaptToRequestOutcome(accountId: string, success: boolean, errorType?: string) {
    const accountLimit = this.accountLimits.get(accountId);
    if (!accountLimit) return;
    
    const learningRate = this.config.adaptiveSettings.learningRate;
    
    if (!success && errorType) {
      if (errorType.includes('rate_limit') || errorType.includes('429')) {
        // Increase cooldown for rate limit errors
        accountLimit.cooldownMultiplier *= (1 + learningRate);
        accountLimit.requestsPerHour = Math.max(1, Math.floor(accountLimit.requestsPerHour * (1 - learningRate)));
      } else if (errorType.includes('blocked') || errorType.includes('banned')) {
        // Significantly reduce limits for blocked accounts
        accountLimit.cooldownMultiplier *= (1 + learningRate * 2);
        accountLimit.requestsPerHour = Math.max(1, Math.floor(accountLimit.requestsPerHour * 0.5));
      }
    } else if (success) {
      // Gradually restore limits on success
      accountLimit.cooldownMultiplier *= (1 - learningRate * 0.1);
      accountLimit.cooldownMultiplier = Math.max(0.5, accountLimit.cooldownMultiplier);
    }
  }

  private startAdaptiveAdjustments() {
    this.adaptationInterval = setInterval(() => {
      this.performAdaptiveAdjustments();
    }, this.config.adaptiveSettings.adaptationInterval);
  }

  private performAdaptiveAdjustments() {
    console.log('🔄 Performing adaptive rate limit adjustments...');
    
    for (const [accountId, accountLimit] of this.accountLimits.entries()) {
      const account = this.accountPool.getAccount(accountId);
      if (!account) continue;
      
      // Analyze recent performance
      const recentRequests = accountLimit.requestHistory.filter(
        entry => Date.now() - entry.timestamp < 60 * 60 * 1000 // Last hour
      );
      
      if (recentRequests.length === 0) continue;
      
      const successRate = recentRequests.filter(r => r.success).length / recentRequests.length;
      
      // Adjust limits based on success rate
      if (successRate > 0.95 && account.health.score > 80) {
        // High success rate - can increase limits slightly
        accountLimit.requestsPerHour = Math.min(
          this.config.accountLimits.defaultRequestsPerHour * 1.2,
          accountLimit.requestsPerHour * 1.05
        );
      } else if (successRate < 0.8) {
        // Low success rate - reduce limits
        accountLimit.requestsPerHour = Math.max(
          1,
          accountLimit.requestsPerHour * 0.9
        );
      }
    }
  }

  /**
   * Get rate limiting statistics
   */
  getStats() {
    const accountStats = Array.from(this.accountLimits.values()).map(limit => {
      const recentRequests = limit.requestHistory.filter(
        entry => Date.now() - entry.timestamp < 60 * 60 * 1000
      );
      
      return {
        accountId: limit.accountId,
        hourlyRequests: recentRequests.length,
        hourlyLimit: limit.requestsPerHour,
        successRate: recentRequests.length > 0 
          ? recentRequests.filter(r => r.success).length / recentRequests.length 
          : 0,
        cooldownMultiplier: limit.cooldownMultiplier,
        lastRequestTime: limit.lastRequestTime
      };
    });
    
    return {
      globalRequestCount: this.globalRequestCount,
      totalAccounts: this.accountLimits.size,
      activeAccounts: accountStats.filter(s => s.hourlyRequests > 0).length,
      averageSuccessRate: accountStats.reduce((sum, s) => sum + s.successRate, 0) / accountStats.length,
      accountStats
    };
  }

  /**
   * Cleanup
   */
  cleanup() {
    if (this.adaptationInterval) {
      clearInterval(this.adaptationInterval);
    }
  }
}
