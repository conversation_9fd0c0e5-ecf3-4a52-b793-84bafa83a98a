// Smart Instagram Account Pool Management System
// Handles ~100 Instagram accounts with intelligent request distribution

import fs from 'fs/promises';
import path from 'path';
import { InstagramSessionGenerator, GeneratedSession } from './instagram-session-generator';

export interface AccountCredentials {
  username: string;
  password: string;
  twoFactorKey?: string; // TOTP secret key for 2FA authentication
  proxy?: string;
  userAgent?: string;
  country?: string;
  priority?: number; // 1-10, higher = more reliable
  tags?: string[]; // e.g., ['premium', 'verified', 'aged']
  notes?: string; // Additional notes about the account
}

export interface AccountHealth {
  score: number; // 0-100, higher = healthier
  lastHealthCheck: number;
  consecutiveFailures: number;
  consecutiveSuccesses: number;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  lastFailureReason?: string;
  riskFactors: string[];
  predictedLifespan: number; // estimated days until potential ban
}

export interface AccountUsageStats {
  hourlyRequests: number[];
  dailyRequests: number;
  weeklyRequests: number;
  monthlyRequests: number;
  lastUsed: number;
  cooldownUntil: number;
  requestHistory: RequestHistoryEntry[];
}

export interface RequestHistoryEntry {
  timestamp: number;
  success: boolean;
  responseTime: number;
  errorType?: string;
  targetUsername?: string;
  ipAddress?: string;
}

export interface ManagedAccount {
  id: string;
  credentials: AccountCredentials;
  session: GeneratedSession;
  health: AccountHealth;
  usage: AccountUsageStats;
  status: 'active' | 'cooling' | 'blocked' | 'maintenance' | 'retired';
  createdAt: number;
  lastValidated: number;
  metadata: {
    accountAge?: number; // days since creation
    followerCount?: number;
    followingCount?: number;
    postsCount?: number;
    isVerified?: boolean;
    isBusiness?: boolean;
    hasProfilePicture?: boolean;
  };
}

export interface DistributionStrategy {
  name: string;
  weight: number;
  criteria: (account: ManagedAccount) => number; // returns score 0-100
  cooldownMultiplier: number;
}

export interface PoolConfiguration {
  maxAccountsPerHour: number;
  maxRequestsPerAccount: number;
  minCooldownBetweenRequests: number; // milliseconds
  maxCooldownBetweenRequests: number; // milliseconds
  healthCheckInterval: number; // milliseconds
  retirementThreshold: number; // health score below which account is retired
  distributionStrategies: DistributionStrategy[];
  riskThresholds: {
    lowRisk: number;
    mediumRisk: number;
    highRisk: number;
  };
}

export class SmartAccountPoolManager {
  private accounts: Map<string, ManagedAccount> = new Map();
  private sessionGenerator: InstagramSessionGenerator;
  private config: PoolConfiguration;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private usageResetInterval: NodeJS.Timeout | null = null;
  private readonly dataPath = path.join(process.cwd(), 'data', 'account-pool.json');
  private readonly backupPath = path.join(process.cwd(), 'data', 'account-pool-backup.json');

  constructor(config?: Partial<PoolConfiguration>) {
    this.sessionGenerator = new InstagramSessionGenerator();
    this.config = {
      maxAccountsPerHour: 50,
      maxRequestsPerAccount: 20,
      minCooldownBetweenRequests: 30000, // 30 seconds
      maxCooldownBetweenRequests: 300000, // 5 minutes
      healthCheckInterval: 300000, // 5 minutes
      retirementThreshold: 30,
      distributionStrategies: this.getDefaultDistributionStrategies(),
      riskThresholds: {
        lowRisk: 25,
        mediumRisk: 50,
        highRisk: 75
      },
      ...config
    };

    this.initialize();
  }

  private getDefaultDistributionStrategies(): DistributionStrategy[] {
    return [
      {
        name: 'health_priority',
        weight: 0.4,
        criteria: (account) => account.health.score,
        cooldownMultiplier: 1.0
      },
      {
        name: 'usage_balance',
        weight: 0.3,
        criteria: (account) => {
          const maxDaily = this.config.maxRequestsPerAccount;
          const used = account.usage.dailyRequests;
          return Math.max(0, 100 - (used / maxDaily) * 100);
        },
        cooldownMultiplier: 1.2
      },
      {
        name: 'account_quality',
        weight: 0.2,
        criteria: (account) => {
          let score = account.credentials.priority || 5;
          if (account.metadata.isVerified) score += 2;
          if (account.metadata.hasProfilePicture) score += 1;
          if (account.metadata.followerCount && account.metadata.followerCount > 100) score += 2;
          return Math.min(100, score * 10);
        },
        cooldownMultiplier: 0.8
      },
      {
        name: 'geographic_distribution',
        weight: 0.1,
        criteria: (account) => {
          // Prefer accounts from different countries for distribution
          const country = account.credentials.country || 'unknown';
          const recentCountries = this.getRecentlyUsedCountries();
          return recentCountries.includes(country) ? 30 : 100;
        },
        cooldownMultiplier: 1.0
      }
    ];
  }

  private async initialize() {
    await this.ensureDataDirectory();
    await this.loadAccountPool();
    this.startHealthMonitoring();
    this.startUsageReset();
  }

  private async ensureDataDirectory() {
    const dataDir = path.dirname(this.dataPath);
    try {
      await fs.access(dataDir);
    } catch {
      await fs.mkdir(dataDir, { recursive: true });
    }
  }

  private async loadAccountPool() {
    try {
      const data = await fs.readFile(this.dataPath, 'utf-8');
      const poolData = JSON.parse(data);

      for (const accountData of poolData.accounts || []) {
        this.accounts.set(accountData.id, accountData);
      }

      console.log(`📊 Loaded ${this.accounts.size} accounts from pool`);
    } catch (error) {
      console.log('📝 No existing account pool found, trying to load from credentials file');
      await this.loadAccountsFromCredentialsFile();
    }
  }

  private async loadAccountsFromCredentialsFile() {
    const credentialsPath = path.join(process.cwd(), 'data', 'accounts.json');
    try {
      await this.addAccountsFromFile(credentialsPath);
      console.log('📥 Loaded accounts from credentials file');
    } catch (error) {
      console.log('📝 No credential files found, starting with empty pool');
    }
  }

  private async saveAccountPool() {
    try {
      // Create backup first
      try {
        await fs.copyFile(this.dataPath, this.backupPath);
      } catch {
        // Backup failed, but continue
      }

      const poolData = {
        accounts: Array.from(this.accounts.values()),
        lastUpdated: Date.now(),
        totalAccounts: this.accounts.size,
        metadata: {
          version: '1.0.0',
          generatedBy: 'smart-account-pool-manager'
        }
      };

      await fs.writeFile(this.dataPath, JSON.stringify(poolData, null, 2));
    } catch (error) {
      console.error('Failed to save account pool:', error);
    }
  }

  private getRecentlyUsedCountries(): string[] {
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    const countries = new Set<string>();
    
    for (const account of this.accounts.values()) {
      if (account.usage.lastUsed > oneHourAgo && account.credentials.country) {
        countries.add(account.credentials.country);
      }
    }
    
    return Array.from(countries);
  }

  private startHealthMonitoring() {
    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthChecks();
    }, this.config.healthCheckInterval);
  }

  private startUsageReset() {
    // Reset daily counters at midnight
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    
    const msUntilMidnight = tomorrow.getTime() - now.getTime();
    
    setTimeout(() => {
      this.resetDailyCounters();
      this.usageResetInterval = setInterval(() => {
        this.resetDailyCounters();
      }, 24 * 60 * 60 * 1000);
    }, msUntilMidnight);
  }

  private resetDailyCounters() {
    for (const account of this.accounts.values()) {
      account.usage.dailyRequests = 0;
      account.usage.hourlyRequests = new Array(24).fill(0);
    }
    console.log('🔄 Reset daily usage counters for all accounts');
  }

  private async performHealthChecks() {
    console.log('🏥 Performing health checks on all accounts...');
    
    const healthCheckPromises = Array.from(this.accounts.values()).map(account => 
      this.checkAccountHealth(account)
    );
    
    await Promise.allSettled(healthCheckPromises);
    await this.saveAccountPool();
    
    console.log('✅ Health checks completed');
  }

  private async checkAccountHealth(account: ManagedAccount): Promise<void> {
    try {
      // Validate session
      const isValid = await this.validateAccountSession(account);
      
      if (isValid) {
        account.health.consecutiveSuccesses++;
        account.health.consecutiveFailures = 0;
        account.status = 'active';
      } else {
        account.health.consecutiveFailures++;
        account.health.consecutiveSuccesses = 0;
        
        if (account.health.consecutiveFailures >= 3) {
          account.status = 'maintenance';
        }
      }
      
      // Update health score
      account.health.score = this.calculateHealthScore(account);
      account.health.lastHealthCheck = Date.now();
      
      // Check if account should be retired
      if (account.health.score < this.config.retirementThreshold) {
        account.status = 'retired';
        console.log(`🚫 Account ${account.credentials.username} retired due to low health score`);
      }
      
    } catch (error) {
      console.error(`Health check failed for ${account.credentials.username}:`, error);
      account.health.consecutiveFailures++;
      account.health.lastFailureReason = error instanceof Error ? error.message : 'Unknown error';
    }
  }

  private async validateAccountSession(account: ManagedAccount): Promise<boolean> {
    // Implementation would validate the session by making a test request
    // For now, return true as placeholder
    return true;
  }

  private calculateHealthScore(account: ManagedAccount): number {
    const health = account.health;
    let score = 100;

    // Penalize consecutive failures
    score -= health.consecutiveFailures * 10;

    // Reward consecutive successes
    score += Math.min(health.consecutiveSuccesses * 2, 20);

    // Consider success rate
    if (health.totalRequests > 0) {
      const successRate = (health.successfulRequests / health.totalRequests) * 100;
      score = (score + successRate) / 2;
    }

    // Penalize risk factors
    score -= health.riskFactors.length * 5;

    return Math.max(0, Math.min(100, score));
  }

  // Public API Methods

  /**
   * Add accounts from credentials file
   */
  async addAccountsFromFile(filePath: string): Promise<void> {
    console.log(`📥 Loading accounts from ${filePath}`);

    try {
      const fileContent = await fs.readFile(filePath, 'utf-8');
      let credentials: AccountCredentials[];

      if (filePath.endsWith('.json')) {
        credentials = JSON.parse(fileContent);
      } else if (filePath.endsWith('.csv')) {
        credentials = this.parseCSVCredentials(fileContent);
      } else {
        credentials = this.parseTextCredentials(fileContent);
      }

      console.log(`🔄 Generating sessions for ${credentials.length} accounts...`);

      // Generate sessions in batches
      const batchSize = 5;
      for (let i = 0; i < credentials.length; i += batchSize) {
        const batch = credentials.slice(i, i + batchSize);
        await this.processBatch(batch);

        // Add delay between batches to avoid detection
        if (i + batchSize < credentials.length) {
          await this.delay(30000); // 30 second delay between batches
        }
      }

      await this.saveAccountPool();
      console.log(`✅ Successfully added ${credentials.length} accounts to pool`);

    } catch (error) {
      console.error('Failed to add accounts from file:', error);
      throw error;
    }
  }

  private parseCSVCredentials(content: string): AccountCredentials[] {
    const lines = content.trim().split('\n');
    const headers = lines[0].split(',').map(h => h.trim());

    return lines.slice(1).map(line => {
      const values = line.split(',').map(v => v.trim());
      const cred: any = {};

      headers.forEach((header, index) => {
        if (values[index]) {
          cred[header] = values[index];
        }
      });

      return cred as AccountCredentials;
    });
  }

  private parseTextCredentials(content: string): AccountCredentials[] {
    const lines = content.trim().split('\n');

    return lines.map(line => {
      const parts = line.split(':');
      if (parts.length >= 2) {
        const credentials: AccountCredentials = {
          username: parts[0].trim(),
          password: parts[1].trim()
        };

        // Handle different formats:
        // Format 1: username:password:2FA
        // Format 2: username:password:proxy:country
        if (parts.length >= 3) {
          const thirdPart = parts[2].trim();
          // Check if third part looks like a 2FA key (typically 32 characters, alphanumeric)
          if (thirdPart.length >= 16 && /^[A-Z0-9]+$/.test(thirdPart)) {
            credentials.twoFactorKey = thirdPart;
          } else {
            credentials.proxy = thirdPart;
          }
        }

        if (parts.length >= 4) {
          credentials.country = parts[3].trim();
        }

        return credentials;
      }
      throw new Error(`Invalid credential format: ${line}`);
    });
  }

  private async processBatch(credentials: AccountCredentials[]): Promise<void> {
    const sessionPromises = credentials.map(async (cred) => {
      try {
        const session = await this.sessionGenerator.generateSingleSession(cred);
        if (session) {
          const account = this.createManagedAccount(cred, session);
          this.accounts.set(account.id, account);
          console.log(`✅ Added account: ${cred.username}`);
        }
      } catch (error) {
        console.error(`❌ Failed to add account ${cred.username}:`, error);
      }
    });

    await Promise.allSettled(sessionPromises);
  }

  private createManagedAccount(credentials: AccountCredentials, session: GeneratedSession): ManagedAccount {
    const now = Date.now();

    return {
      id: this.generateAccountId(credentials.username),
      credentials,
      session,
      health: {
        score: 100,
        lastHealthCheck: now,
        consecutiveFailures: 0,
        consecutiveSuccesses: 0,
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        averageResponseTime: 0,
        riskFactors: [],
        predictedLifespan: 365 // Default 1 year
      },
      usage: {
        hourlyRequests: new Array(24).fill(0),
        dailyRequests: 0,
        weeklyRequests: 0,
        monthlyRequests: 0,
        lastUsed: 0,
        cooldownUntil: 0,
        requestHistory: []
      },
      status: 'active',
      createdAt: now,
      lastValidated: now,
      metadata: {}
    };
  }

  private generateAccountId(username: string): string {
    return `acc_${username}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get the best account for making a request
   */
  async getBestAccount(targetUsername?: string): Promise<ManagedAccount | null> {
    const availableAccounts = this.getAvailableAccounts();

    if (availableAccounts.length === 0) {
      console.warn('⚠️ No available accounts in pool');
      return null;
    }

    // Calculate scores for each account using distribution strategies
    const scoredAccounts = availableAccounts.map(account => ({
      account,
      score: this.calculateDistributionScore(account, targetUsername)
    }));

    // Sort by score (highest first)
    scoredAccounts.sort((a, b) => b.score - a.score);

    // Add some randomness to prevent always using the same account
    const topCandidates = scoredAccounts.slice(0, Math.min(5, scoredAccounts.length));
    const selectedIndex = this.weightedRandomSelection(topCandidates.map(c => c.score));

    const selectedAccount = topCandidates[selectedIndex].account;

    // Mark account as used and set cooldown
    this.markAccountUsed(selectedAccount, targetUsername);

    console.log(`🎯 Selected account: ${selectedAccount.credentials.username} (score: ${topCandidates[selectedIndex].score.toFixed(2)})`);

    return selectedAccount;
  }

  private getAvailableAccounts(): ManagedAccount[] {
    const now = Date.now();

    return Array.from(this.accounts.values()).filter(account => {
      // Check status
      if (account.status !== 'active') return false;

      // Check cooldown
      if (account.usage.cooldownUntil > now) return false;

      // Check daily limits
      if (account.usage.dailyRequests >= this.config.maxRequestsPerAccount) return false;

      // Check health
      if (account.health.score < 50) return false;

      return true;
    });
  }

  private calculateDistributionScore(account: ManagedAccount, targetUsername?: string): number {
    let totalScore = 0;

    for (const strategy of this.config.distributionStrategies) {
      const strategyScore = strategy.criteria(account);
      totalScore += strategyScore * strategy.weight;
    }

    // Apply penalties for recent usage
    const timeSinceLastUse = Date.now() - account.usage.lastUsed;
    const hoursSinceLastUse = timeSinceLastUse / (60 * 60 * 1000);

    if (hoursSinceLastUse < 1) {
      totalScore *= 0.5; // Heavy penalty for recent use
    } else if (hoursSinceLastUse < 6) {
      totalScore *= 0.8; // Moderate penalty
    }

    return Math.max(0, totalScore);
  }

  private weightedRandomSelection(weights: number[]): number {
    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
    let random = Math.random() * totalWeight;

    for (let i = 0; i < weights.length; i++) {
      random -= weights[i];
      if (random <= 0) {
        return i;
      }
    }

    return weights.length - 1;
  }

  private markAccountUsed(account: ManagedAccount, targetUsername?: string) {
    const now = Date.now();
    const hour = new Date().getHours();

    // Update usage stats
    account.usage.lastUsed = now;
    account.usage.dailyRequests++;
    account.usage.weeklyRequests++;
    account.usage.monthlyRequests++;
    account.usage.hourlyRequests[hour]++;

    // Set cooldown with some randomness
    const baseCooldown = this.config.minCooldownBetweenRequests;
    const maxCooldown = this.config.maxCooldownBetweenRequests;
    const randomCooldown = baseCooldown + Math.random() * (maxCooldown - baseCooldown);

    account.usage.cooldownUntil = now + randomCooldown;

    // Add to request history
    account.usage.requestHistory.push({
      timestamp: now,
      success: true, // Will be updated later
      responseTime: 0, // Will be updated later
      targetUsername
    });

    // Keep only last 100 requests in history
    if (account.usage.requestHistory.length > 100) {
      account.usage.requestHistory = account.usage.requestHistory.slice(-100);
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Mark request as successful
   */
  markRequestSuccess(accountId: string, responseTime: number, targetUsername?: string) {
    const account = this.accounts.get(accountId);
    if (!account) return;

    account.health.totalRequests++;
    account.health.successfulRequests++;
    account.health.consecutiveSuccesses++;
    account.health.consecutiveFailures = 0;

    // Update average response time
    const totalTime = account.health.averageResponseTime * (account.health.totalRequests - 1);
    account.health.averageResponseTime = (totalTime + responseTime) / account.health.totalRequests;

    // Update request history
    const lastRequest = account.usage.requestHistory[account.usage.requestHistory.length - 1];
    if (lastRequest && lastRequest.timestamp > Date.now() - 60000) { // Within last minute
      lastRequest.success = true;
      lastRequest.responseTime = responseTime;
      lastRequest.targetUsername = targetUsername;
    }

    // Update health score
    account.health.score = this.calculateHealthScore(account);

    // Remove risk factors on success
    if (account.health.consecutiveSuccesses >= 5) {
      account.health.riskFactors = account.health.riskFactors.filter(
        factor => !['rate_limit', 'temporary_block'].includes(factor)
      );
    }
  }

  /**
   * Mark request as failed
   */
  markRequestFailure(accountId: string, error: string, errorType?: string) {
    const account = this.accounts.get(accountId);
    if (!account) return;

    account.health.totalRequests++;
    account.health.failedRequests++;
    account.health.consecutiveFailures++;
    account.health.consecutiveSuccesses = 0;
    account.health.lastFailureReason = error;

    // Update request history
    const lastRequest = account.usage.requestHistory[account.usage.requestHistory.length - 1];
    if (lastRequest && lastRequest.timestamp > Date.now() - 60000) {
      lastRequest.success = false;
      lastRequest.errorType = errorType;
    }

    // Add risk factors based on error type
    if (error.includes('rate limit') || error.includes('429')) {
      this.addRiskFactor(account, 'rate_limit');
      account.status = 'cooling';
      account.usage.cooldownUntil = Date.now() + (60 * 60 * 1000); // 1 hour cooldown
    } else if (error.includes('login') || error.includes('401')) {
      this.addRiskFactor(account, 'authentication_failure');
      account.status = 'maintenance';
    } else if (error.includes('challenge') || error.includes('captcha')) {
      this.addRiskFactor(account, 'challenge_required');
      account.status = 'blocked';
    } else if (error.includes('banned') || error.includes('suspended')) {
      this.addRiskFactor(account, 'account_banned');
      account.status = 'retired';
    }

    // Update health score
    account.health.score = this.calculateHealthScore(account);

    // Auto-retire account if too many failures
    if (account.health.consecutiveFailures >= 10) {
      account.status = 'retired';
      console.log(`🚫 Account ${account.credentials.username} auto-retired due to consecutive failures`);
    }
  }

  private addRiskFactor(account: ManagedAccount, factor: string) {
    if (!account.health.riskFactors.includes(factor)) {
      account.health.riskFactors.push(factor);
    }
  }

  /**
   * Get pool statistics
   */
  getPoolStats() {
    const accounts = Array.from(this.accounts.values());
    const now = Date.now();

    const stats = {
      total: accounts.length,
      active: accounts.filter(a => a.status === 'active').length,
      cooling: accounts.filter(a => a.status === 'cooling').length,
      blocked: accounts.filter(a => a.status === 'blocked').length,
      maintenance: accounts.filter(a => a.status === 'maintenance').length,
      retired: accounts.filter(a => a.status === 'retired').length,
      available: this.getAvailableAccounts().length,
      averageHealth: accounts.reduce((sum, a) => sum + a.health.score, 0) / accounts.length,
      totalRequests: accounts.reduce((sum, a) => sum + a.health.totalRequests, 0),
      successRate: this.calculateOverallSuccessRate(accounts),
      dailyRequestsUsed: accounts.reduce((sum, a) => sum + a.usage.dailyRequests, 0),
      accountsInCooldown: accounts.filter(a => a.usage.cooldownUntil > now).length,
      riskDistribution: this.getRiskDistribution(accounts),
      countryDistribution: this.getCountryDistribution(accounts),
      lastHealthCheck: Math.min(...accounts.map(a => a.health.lastHealthCheck))
    };

    return stats;
  }

  private calculateOverallSuccessRate(accounts: ManagedAccount[]): number {
    const totalRequests = accounts.reduce((sum, a) => sum + a.health.totalRequests, 0);
    const successfulRequests = accounts.reduce((sum, a) => sum + a.health.successfulRequests, 0);

    return totalRequests > 0 ? (successfulRequests / totalRequests) * 100 : 0;
  }

  private getRiskDistribution(accounts: ManagedAccount[]) {
    const distribution = { low: 0, medium: 0, high: 0 };

    for (const account of accounts) {
      if (account.health.score >= this.config.riskThresholds.highRisk) {
        distribution.low++;
      } else if (account.health.score >= this.config.riskThresholds.mediumRisk) {
        distribution.medium++;
      } else {
        distribution.high++;
      }
    }

    return distribution;
  }

  private getCountryDistribution(accounts: ManagedAccount[]) {
    const distribution: Record<string, number> = {};

    for (const account of accounts) {
      const country = account.credentials.country || 'unknown';
      distribution[country] = (distribution[country] || 0) + 1;
    }

    return distribution;
  }

  /**
   * Refresh unhealthy accounts
   */
  async refreshUnhealthyAccounts(credentialsFile: string): Promise<void> {
    const unhealthyAccounts = Array.from(this.accounts.values()).filter(
      account => account.health.score < 50 || account.status === 'maintenance'
    );

    if (unhealthyAccounts.length === 0) {
      console.log('✅ No unhealthy accounts to refresh');
      return;
    }

    console.log(`🔄 Refreshing ${unhealthyAccounts.length} unhealthy accounts...`);

    // Extract credentials for unhealthy accounts
    const credentialsToRefresh = unhealthyAccounts.map(account => account.credentials);

    // Generate new sessions
    const newSessions = await this.sessionGenerator.generateSessionsFromFile(credentialsFile);

    // Update accounts with new sessions
    for (const session of newSessions) {
      const existingAccount = Array.from(this.accounts.values()).find(
        account => account.credentials.username === session.username
      );

      if (existingAccount) {
        existingAccount.session = session;
        existingAccount.status = 'active';
        existingAccount.health.consecutiveFailures = 0;
        existingAccount.health.riskFactors = [];
        existingAccount.lastValidated = Date.now();
        console.log(`✅ Refreshed session for ${session.username}`);
      }
    }

    await this.saveAccountPool();
    console.log(`🎉 Successfully refreshed ${newSessions.length} accounts`);
  }

  /**
   * Cleanup and shutdown
   */
  async cleanup() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    if (this.usageResetInterval) {
      clearInterval(this.usageResetInterval);
    }

    await this.saveAccountPool();
    await this.sessionGenerator.cleanup();

    console.log('🧹 Smart Account Pool Manager cleaned up');
  }

  /**
   * Get account by ID
   */
  getAccount(accountId: string): ManagedAccount | null {
    return this.accounts.get(accountId) || null;
  }

  /**
   * Remove account from pool
   */
  removeAccount(accountId: string): boolean {
    return this.accounts.delete(accountId);
  }

  /**
   * Get all accounts (for admin purposes)
   */
  getAllAccounts(): ManagedAccount[] {
    return Array.from(this.accounts.values());
  }
}
