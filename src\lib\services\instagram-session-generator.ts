// Automatische Instagram Session Generierung
// Generiert Session Cookies aus Login-Daten automatisch

import puppeteer from 'puppeteer';
import fs from 'fs/promises';
import path from 'path';
import { authenticator } from 'otplib';

interface LoginCredentials {
  username: string;
  password: string;
  twoFactorKey?: string; // TOTP secret key for 2FA
  proxy?: string;
  userAgent?: string;
  country?: string;
}

export interface GeneratedSession {
  username: string;
  sessionid: string;
  csrftoken: string;
  ds_user_id: string;
  mid: string;
  ig_did: string;
  userAgent: string;
  proxy?: string;
  country?: string;
  createdAt: number;
  isValid: boolean;
  lastValidated: number;
}

export class InstagramSessionGenerator {
  private browser: any = null;
  private readonly maxConcurrentLogins = 5;
  private readonly loginTimeout = 60000; // 60 Sekunden
  private readonly userAgents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/119.0'
  ];

  async initialize() {
    if (this.browser) return;

    try {
      console.log('🚀 Initializing browser with bundled Chromium...');

      this.browser = await puppeteer.launch({
        headless: process.env.HEADLESS_MODE !== 'false',
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor',
          '--disable-background-timer-throttling',
          '--disable-backgrounding-occluded-windows',
          '--disable-renderer-backgrounding'
        ]
      });

      console.log('✅ Browser initialized for session generation');
    } catch (error) {
      console.error('❌ Failed to initialize browser:', error);
      throw error;
    }
  }

  async generateSessionsFromFile(filePath: string): Promise<GeneratedSession[]> {
    console.log(`🔄 Loading login credentials from ${filePath}`);
    
    const credentials = await this.loadCredentials(filePath);
    console.log(`📋 Found ${credentials.length} login credentials`);

    const sessions: GeneratedSession[] = [];
    const batches = this.chunkArray(credentials, this.maxConcurrentLogins);

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      console.log(`\n🚀 Processing batch ${i + 1}/${batches.length} (${batch.length} accounts)`);

      const batchPromises = batch.map((cred, index) => 
        this.generateSessionWithRetry(cred, `${i * this.maxConcurrentLogins + index + 1}`)
      );

      const batchResults = await Promise.allSettled(batchPromises);
      
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value) {
          sessions.push(result.value);
          console.log(`✅ Session generated for ${batch[index].username}`);
        } else {
          console.log(`❌ Failed to generate session for ${batch[index].username}`);
        }
      });

      // Pause zwischen Batches um Instagram nicht zu überlasten
      if (i < batches.length - 1) {
        console.log('⏳ Waiting 30 seconds before next batch...');
        await this.delay(30000);
      }
    }

    console.log(`\n🎉 Generated ${sessions.length}/${credentials.length} sessions successfully`);
    return sessions;
  }

  async loadCredentials(filePath: string): Promise<LoginCredentials[]> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      
      if (filePath.endsWith('.json')) {
        return JSON.parse(content);
      } else if (filePath.endsWith('.csv')) {
        return this.parseCSV(content);
      } else if (filePath.endsWith('.txt')) {
        return this.parseTXT(content);
      } else {
        throw new Error('Unsupported file format. Use .json, .csv, or .txt');
      }
    } catch (error) {
      console.error('Failed to load credentials:', error);
      throw error;
    }
  }

  private parseCSV(content: string): LoginCredentials[] {
    const lines = content.trim().split('\n');
    const headers = lines[0].split(',').map(h => h.trim());
    
    return lines.slice(1).map(line => {
      const values = line.split(',').map(v => v.trim());
      const cred: any = {};
      
      headers.forEach((header, index) => {
        cred[header] = values[index];
      });
      
      return cred as LoginCredentials;
    });
  }

  private parseTXT(content: string): LoginCredentials[] {
    const lines = content.trim().split('\n');

    return lines.map(line => {
      const parts = line.split(':');
      if (parts.length < 2) {
        throw new Error(`Invalid format in line: ${line}. Expected username:password or username:password:2FA`);
      }

      const credentials: LoginCredentials = {
        username: parts[0].trim(),
        password: parts[1].trim()
      };

      // Handle different formats:
      // Format 1: username:password:2FA
      // Format 2: username:password:proxy:country
      if (parts.length >= 3) {
        const thirdPart = parts[2].trim();
        // Check if third part looks like a 2FA key (typically 32 characters, alphanumeric)
        if (thirdPart.length >= 16 && /^[A-Z0-9]+$/.test(thirdPart)) {
          credentials.twoFactorKey = thirdPart;
        } else {
          credentials.proxy = thirdPart;
        }
      }

      if (parts.length >= 4) {
        credentials.country = parts[3].trim();
      }

      return credentials;
    });
  }

  private async generateSessionWithRetry(credentials: LoginCredentials, accountNumber: string): Promise<GeneratedSession | null> {
    const maxRetries = 3;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔐 [${accountNumber}] Attempting login for ${credentials.username} (attempt ${attempt}/${maxRetries})`);
        
        const session = await this.generateSingleSession(credentials);
        if (session) {
          return session;
        }
      } catch (error) {
        console.log(`❌ [${accountNumber}] Attempt ${attempt} failed:`, error instanceof Error ? error.message : 'Unknown error');
        
        if (attempt < maxRetries) {
          const delay = attempt * 10000; // 10s, 20s, 30s
          console.log(`⏳ [${accountNumber}] Waiting ${delay/1000}s before retry...`);
          await this.delay(delay);
        }
      }
    }
    
    return null;
  }

  public async generateSingleSession(credentials: LoginCredentials): Promise<GeneratedSession | null> {
    let page = null;
    
    try {
      await this.initialize();
      
      page = await this.browser.newPage();
      
      // Set user agent
      const userAgent = credentials.userAgent || this.getRandomUserAgent();
      await page.setUserAgent(userAgent);
      
      // Set viewport
      await page.setViewport({ 
        width: 1366 + Math.floor(Math.random() * 200), 
        height: 768 + Math.floor(Math.random() * 200) 
      });

      // Configure proxy if provided
      if (credentials.proxy) {
        // Proxy configuration would go here
        console.log(`🌐 Using proxy: ${credentials.proxy}`);
      }

      // Block unnecessary resources to speed up
      await page.setRequestInterception(true);
      page.on('request', (req: any) => {
        const resourceType = req.resourceType();
        if (['image', 'stylesheet', 'font', 'media'].includes(resourceType)) {
          req.abort();
        } else {
          req.continue();
        }
      });

      // Navigate to Instagram
      console.log(`🌐 Navigating to Instagram...`);
      await page.goto('https://www.instagram.com/accounts/login/', {
        waitUntil: 'domcontentloaded',
        timeout: this.loginTimeout
      });

      // Wait a bit for page to load
      await this.delay(3000);

      // Try multiple selectors for username field
      console.log(`🔍 Looking for login form...`);
      let usernameSelector = null;
      const usernameSelectors = [
        'input[name="username"]',
        'input[aria-label="Phone number, username, or email"]',
        'input[placeholder*="username"]',
        'input[placeholder*="email"]',
        'input[type="text"]'
      ];

      for (const selector of usernameSelectors) {
        try {
          await page.waitForSelector(selector, { timeout: 3000 });
          usernameSelector = selector;
          console.log(`✅ Found username field: ${selector}`);
          break;
        } catch (e) {
          console.log(`❌ Selector not found: ${selector}`);
        }
      }

      if (!usernameSelector) {
        throw new Error('Could not find username input field');
      }
      
      // Human-like delays
      await this.humanDelay();

      // Fill username
      console.log(`📝 Filling username: ${credentials.username}`);
      await page.type(usernameSelector, credentials.username, { delay: 100 });
      await this.humanDelay();

      // Find password field
      let passwordSelector = null;
      const passwordSelectors = [
        'input[name="password"]',
        'input[aria-label="Password"]',
        'input[type="password"]'
      ];

      for (const selector of passwordSelectors) {
        try {
          await page.waitForSelector(selector, { timeout: 2000 });
          passwordSelector = selector;
          console.log(`✅ Found password field: ${selector}`);
          break;
        } catch (e) {
          // Continue searching
        }
      }

      if (!passwordSelector) {
        throw new Error('Could not find password input field');
      }

      // Fill password
      console.log(`🔐 Filling password...`);
      await page.type(passwordSelector, credentials.password, { delay: 100 });
      await this.humanDelay();

      // Find and click submit button
      console.log(`🚀 Submitting login form...`);
      const submitSelectors = [
        'button[type="submit"]',
        'button:contains("Log in")',
        'button:contains("Log In")',
        'div[role="button"]:contains("Log in")'
      ];

      let submitClicked = false;
      for (const selector of submitSelectors) {
        try {
          await page.click(selector);
          submitClicked = true;
          console.log(`✅ Clicked submit button: ${selector}`);
          break;
        } catch (e) {
          // Continue searching
        }
      }

      if (!submitClicked) {
        // Try pressing Enter as fallback
        await page.keyboard.press('Enter');
        console.log(`⌨️ Pressed Enter as fallback`);
      }
      
      // Wait for navigation or error
      console.log(`⏳ Waiting for login result...`);

      try {
        await Promise.race([
          page.waitForNavigation({ waitUntil: 'domcontentloaded', timeout: this.loginTimeout }),
          page.waitForSelector('[data-testid="login-error"]', { timeout: 8000 }).then(() => {
            throw new Error('Login failed - invalid credentials');
          }),
          page.waitForSelector('input[placeholder*="phone"]', { timeout: 8000 }).then(async () => {
            // 2FA required - handle if we have the key
            if (credentials.twoFactorKey) {
              console.log(`🔐 2FA required, generating TOTP code...`);
              await this.handle2FA(page, credentials.twoFactorKey);
            } else {
              throw new Error('2FA required but no 2FA key provided');
            }
          }),
          page.waitForSelector('[aria-label*="Challenge"]', { timeout: 8000 }).then(() => {
            throw new Error('Instagram challenge required - account may be flagged');
          })
        ]);
      } catch (error) {
        // Check if we're actually logged in despite the error
        const currentUrl = page.url();
        console.log(`🔍 Current URL after login attempt: ${currentUrl}`);

        if (!currentUrl.includes('/accounts/login/') && !currentUrl.includes('/challenge/')) {
          console.log(`✅ Login appears successful despite error`);
        } else {
          throw error;
        }
      }

      // Check if login was successful
      const currentUrl = page.url();
      if (currentUrl.includes('/accounts/login/') || currentUrl.includes('/challenge/')) {
        throw new Error('Login failed or challenge required');
      }

      console.log(`✅ Login successful for ${credentials.username}`);

      // Extract cookies
      console.log(`🍪 Extracting session cookies...`);
      const cookies = await page.cookies();
      const sessionCookies = this.extractSessionCookies(cookies);

      console.log(`📋 Extracted cookies:`, {
        sessionid: sessionCookies.sessionid ? '✅' : '❌',
        csrftoken: sessionCookies.csrftoken ? '✅' : '❌',
        ds_user_id: sessionCookies.ds_user_id ? '✅' : '❌',
        mid: sessionCookies.mid ? '✅' : '❌',
        ig_did: sessionCookies.ig_did ? '✅' : '❌'
      });

      if (!sessionCookies.sessionid) {
        throw new Error('Failed to extract sessionid cookie');
      }

      if (!sessionCookies.csrftoken) {
        throw new Error('Failed to extract csrftoken cookie');
      }

      if (!sessionCookies.ds_user_id) {
        throw new Error('Failed to extract ds_user_id cookie');
      }

      // Validate session
      const isValid = await this.validateSession(sessionCookies, userAgent);
      if (!isValid) {
        throw new Error('Generated session is not valid');
      }

      return {
        username: credentials.username,
        sessionid: sessionCookies.sessionid,
        csrftoken: sessionCookies.csrftoken,
        ds_user_id: sessionCookies.ds_user_id,
        mid: sessionCookies.mid || '',
        ig_did: sessionCookies.ig_did || '',
        userAgent,
        proxy: credentials.proxy,
        country: credentials.country,
        createdAt: Date.now(),
        isValid: true,
        lastValidated: Date.now()
      };

    } catch (error) {
      throw error;
    } finally {
      if (page) {
        await page.close();
      }
    }
  }

  private extractSessionCookies(cookies: any[]): any {
    const sessionCookies: any = {};
    
    cookies.forEach(cookie => {
      if (['sessionid', 'csrftoken', 'ds_user_id', 'mid', 'ig_did'].includes(cookie.name)) {
        sessionCookies[cookie.name] = cookie.value;
      }
    });
    
    return sessionCookies;
  }

  private async validateSession(sessionCookies: any, userAgent: string): Promise<boolean> {
    try {
      const axios = await import('axios');

      console.log(`🔍 Validating session for user ID: ${sessionCookies.ds_user_id}`);

      // Try multiple validation endpoints
      const validationUrls = [
        'https://www.instagram.com/',
        'https://www.instagram.com/api/v1/accounts/current_user/',
        'https://www.instagram.com/accounts/edit/'
      ];

      for (const url of validationUrls) {
        try {
          const response = await axios.default.get(url, {
            headers: {
              'Cookie': `sessionid=${sessionCookies.sessionid}; csrftoken=${sessionCookies.csrftoken}; ds_user_id=${sessionCookies.ds_user_id}`,
              'User-Agent': userAgent,
              'X-CSRFToken': sessionCookies.csrftoken
            },
            timeout: 10000,
            maxRedirects: 5
          });

          console.log(`✅ Validation URL ${url}: Status ${response.status}`);

          // Check if we're logged in (not redirected to login page)
          if (response.status === 200 && !response.data.includes('loginForm')) {
            console.log(`✅ Session validation successful`);
            return true;
          }
        } catch (error) {
          console.log(`❌ Validation URL ${url} failed:`, error.response?.status || error.message);
        }
      }

      // If all validation attempts fail, but we have valid cookies, assume success
      if (sessionCookies.sessionid && sessionCookies.csrftoken && sessionCookies.ds_user_id) {
        console.log(`⚠️ Validation failed but cookies look valid, assuming success`);
        return true;
      }

      return false;
    } catch (error) {
      console.log(`❌ Session validation error:`, error.message);
      return false;
    }
  }

  private getRandomUserAgent(): string {
    return this.userAgents[Math.floor(Math.random() * this.userAgents.length)];
  }

  private async humanDelay(): Promise<void> {
    const delay = Math.random() * 2000 + 1000; // 1-3 seconds
    await this.delay(delay);
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  /**
   * Handle 2FA authentication
   */
  private async handle2FA(page: any, twoFactorKey: string): Promise<void> {
    try {
      console.log(`🔐 Handling 2FA authentication...`);

      // Generate TOTP code from the secret key
      const totpCode = authenticator.generate(twoFactorKey);
      console.log(`🔑 Generated TOTP code: ${totpCode}`);

      // Wait for 2FA input field
      await page.waitForSelector('input[placeholder*="phone"], input[name="verificationCode"], input[aria-label*="security code"]', { timeout: 10000 });

      // Find and fill the 2FA code input
      const codeInput = await page.$('input[placeholder*="phone"], input[name="verificationCode"], input[aria-label*="security code"]');
      if (codeInput) {
        await codeInput.click();
        await this.delay(500);
        await codeInput.type(totpCode, { delay: 100 });
        console.log(`✅ Entered 2FA code`);

        // Submit the 2FA form
        const submitButton = await page.$('button[type="submit"], button:contains("Confirm"), button:contains("Submit")');
        if (submitButton) {
          await submitButton.click();
          console.log(`📤 Submitted 2FA form`);

          // Wait for navigation or success
          await Promise.race([
            page.waitForNavigation({ waitUntil: 'domcontentloaded', timeout: 15000 }),
            page.waitForSelector('[data-testid="login-error"]', { timeout: 8000 }).then(() => {
              throw new Error('2FA verification failed');
            })
          ]);

          console.log(`✅ 2FA authentication successful`);
        } else {
          throw new Error('Could not find 2FA submit button');
        }
      } else {
        throw new Error('Could not find 2FA input field');
      }

    } catch (error) {
      console.error(`❌ 2FA authentication failed:`, error);
      throw new Error(`2FA authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }
}
