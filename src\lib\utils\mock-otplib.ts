// Mock otplib implementation for temporary use until the real package is installed
// This allows the application to run without errors

export const authenticator = {
  generate: (secret: string): string => {
    // This is a mock implementation - DO NOT USE IN PRODUCTION
    // Returns a placeholder TOTP code
    console.warn('⚠️ Using mock TOTP generator - install otplib for real 2FA support');
    
    // Generate a simple 6-digit code based on current time and secret
    const timestamp = Math.floor(Date.now() / 30000); // 30-second window
    const hash = simple_hash(secret + timestamp.toString());
    const code = (hash % 1000000).toString().padStart(6, '0');
    
    return code;
  }
};

// Simple hash function for mock TOTP generation
function simple_hash(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash);
}

// Export for compatibility
export default { authenticator };
