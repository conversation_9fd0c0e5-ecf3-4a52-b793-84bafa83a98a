import { NextRequest, NextResponse } from 'next/server';
import { IntelligentSessionManager } from '@/lib/services/intelligent-session-manager';
import { AccountMigrationUtility } from '@/lib/utils/account-migration';
import { config } from '@/lib/config/env';

// Global session manager instance
let sessionManagerInstance: IntelligentSessionManager | null = null;

function getSessionManager(): IntelligentSessionManager {
  if (!sessionManagerInstance) {
    sessionManagerInstance = new IntelligentSessionManager();
  }
  return sessionManagerInstance;
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const action = searchParams.get('action') || 'status';

  try {
    const sessionManager = getSessionManager();

    switch (action) {
      case 'status':
        return await handleGetStatus(sessionManager);
      
      case 'metrics':
        return await handleGetMetrics(sessionManager);
      
      case 'accounts':
        return await handleGetAccounts(sessionManager);
      
      case 'migration-status':
        return await handleMigrationStatus();
      
      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: status, metrics, accounts, migration-status' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Session pool API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    // Validate admin access for write operations
    const adminKey = request.headers.get('x-admin-key');
    if (adminKey !== config.adminApiKey) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const sessionManager = getSessionManager();

    switch (action) {
      case 'initialize':
        return await handleInitialize(sessionManager);
      
      case 'rotate-session':
        return await handleRotateSession(sessionManager, body);
      
      case 'migrate-accounts':
        return await handleMigrateAccounts();
      
      case 'validate-migration':
        return await handleValidateMigration();
      
      case 'rollback-migration':
        return await handleRollbackMigration();
      
      case 'cleanup':
        return await handleCleanup(sessionManager);
      
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Session pool API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

async function handleGetStatus(sessionManager: IntelligentSessionManager) {
  try {
    const status = await sessionManager.getDetailedStatus();
    return NextResponse.json(status);
  } catch (error) {
    // If not initialized, try to initialize
    if (error instanceof Error && error.message.includes('not initialized')) {
      try {
        await sessionManager.initialize();
        const status = await sessionManager.getDetailedStatus();
        return NextResponse.json(status);
      } catch (initError) {
        return NextResponse.json({
          initialized: false,
          error: initError instanceof Error ? initError.message : 'Initialization failed',
          activeSessions: [],
          accountSummary: { total: 0, healthy: 0, inCooldown: 0, withErrors: 0 },
          metrics: {
            activeSessions: 0,
            healthyAccounts: 0,
            totalAccounts: 0,
            averageSessionAge: 0,
            requestsPerHour: 0,
            rotationsToday: 0,
            errorRate: 0
          }
        });
      }
    }
    throw error;
  }
}

async function handleGetMetrics(sessionManager: IntelligentSessionManager) {
  const metrics = sessionManager.getSessionPoolMetrics();
  return NextResponse.json({
    success: true,
    metrics,
    timestamp: new Date().toISOString()
  });
}

async function handleGetAccounts(sessionManager: IntelligentSessionManager) {
  const status = await sessionManager.getDetailedStatus();
  return NextResponse.json({
    success: true,
    accountSummary: status.accountSummary,
    activeSessions: status.activeSessions,
    timestamp: new Date().toISOString()
  });
}

async function handleInitialize(sessionManager: IntelligentSessionManager) {
  console.log('🚀 Initializing session manager via API...');
  
  await sessionManager.initialize();
  const status = await sessionManager.getDetailedStatus();
  
  return NextResponse.json({
    success: true,
    message: 'Session manager initialized successfully',
    status,
    timestamp: new Date().toISOString()
  });
}

async function handleRotateSession(sessionManager: IntelligentSessionManager, body: any) {
  const { username } = body;
  
  if (!username) {
    return NextResponse.json(
      { error: 'Username required for session rotation' },
      { status: 400 }
    );
  }
  
  // This would require implementing a public rotation method
  // For now, return a placeholder response
  return NextResponse.json({
    success: true,
    message: `Session rotation requested for ${username}`,
    timestamp: new Date().toISOString()
  });
}

async function handleMigrateAccounts() {
  console.log('🔄 Starting account migration via API...');
  
  const migrationUtility = new AccountMigrationUtility();
  const result = await migrationUtility.migrateAccounts();
  
  return NextResponse.json({
    success: true,
    message: 'Account migration completed',
    result,
    timestamp: new Date().toISOString()
  });
}

async function handleValidateMigration() {
  console.log('🔍 Validating account migration via API...');
  
  const migrationUtility = new AccountMigrationUtility();
  const validation = await migrationUtility.validateMigration();
  
  return NextResponse.json({
    success: true,
    validation,
    timestamp: new Date().toISOString()
  });
}

async function handleRollbackMigration() {
  console.log('🔄 Rolling back account migration via API...');
  
  const migrationUtility = new AccountMigrationUtility();
  const success = await migrationUtility.rollbackMigration();
  
  return NextResponse.json({
    success,
    message: success ? 'Migration rolled back successfully' : 'Rollback failed',
    timestamp: new Date().toISOString()
  });
}

async function handleMigrationStatus() {
  const migrationUtility = new AccountMigrationUtility();
  const stats = await migrationUtility.getAccountStatistics();
  
  return NextResponse.json({
    success: true,
    migrationStatus: {
      basicAccountsExist: stats.basic?.exists || false,
      enhancedAccountsExist: stats.enhanced?.exists || false,
      basicAccountCount: stats.basic?.count || 0,
      enhancedAccountCount: stats.enhanced?.count || 0,
      migrationNeeded: stats.basic?.exists && !stats.enhanced?.exists
    },
    statistics: stats,
    timestamp: new Date().toISOString()
  });
}

async function handleCleanup(sessionManager: IntelligentSessionManager) {
  console.log('🧹 Cleaning up session manager via API...');
  
  await sessionManager.cleanup();
  sessionManagerInstance = null; // Reset instance
  
  return NextResponse.json({
    success: true,
    message: 'Session manager cleaned up successfully',
    timestamp: new Date().toISOString()
  });
}

// Health check endpoint
export async function HEAD() {
  return new NextResponse(null, { status: 200 });
}
