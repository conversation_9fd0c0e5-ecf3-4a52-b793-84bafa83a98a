import { <PERSON>ada<PERSON> } from 'next';
import { Check, X, Star, Shield, Zap, Users, Download, Eye } from 'lucide-react';
import { Head<PERSON> } from '@/components/Header';
import { Footer } from '@/components/Footer';

export const metadata: Metadata = {
  title: 'Instagram Story Viewer Comparison | Best Alternative to Mollygram',
  description: 'Compare our Instagram story viewer with Mollygram, Inflact, StoriesIG and other competitors. See why we offer the best features and reliability.',
  keywords: 'Instagram story viewer comparison, Mollygram alternative, best Instagram story viewer, StoriesIG vs Mollygram, Instagram story downloader comparison',
  openGraph: {
    title: 'Instagram Story Viewer Comparison - Best Alternative',
    description: 'Compare features, reliability, and performance of top Instagram story viewers.',
    type: 'website',
  },
};

const competitors = [
  {
    name: 'Our Service',
    isOurs: true,
    logo: '🚀',
    tagline: 'Smart Account Management',
    features: {
      anonymousViewing: true,
      hdDownloads: true,
      videoPlayback: true,
      mobileOptimized: true,
      noRegistration: true,
      fastLoading: true,
      reliableUptime: true,
      smartDistribution: true,
      accountPooling: true,
      rateLimiting: true,
      circuitBreakers: true,
      healthMonitoring: true,
      concurrentUsers: '100+',
      dailyLimit: '3 (Free)',
      videoQuality: 'HD',
      loadingSpeed: 'Sub-2s',
      uptime: '99%+',
      privacy: 'Excellent'
    },
    pros: [
      'Advanced smart account management',
      'High-traffic resilience (100+ concurrent users)',
      'Predictive health monitoring',
      'Circuit breaker protection',
      'Intelligent request distribution',
      'No data storage policy'
    ],
    cons: [
      'Newer service (but proven technology)',
      'Limited free tier (3 requests/day)'
    ]
  },
  {
    name: 'Mollygram',
    isOurs: false,
    logo: '📱',
    tagline: 'Popular Choice',
    features: {
      anonymousViewing: true,
      hdDownloads: true,
      videoPlayback: true,
      mobileOptimized: true,
      noRegistration: true,
      fastLoading: true,
      reliableUptime: true,
      smartDistribution: false,
      accountPooling: false,
      rateLimiting: false,
      circuitBreakers: false,
      healthMonitoring: false,
      concurrentUsers: '50+',
      dailyLimit: 'Unlimited*',
      videoQuality: 'HD',
      loadingSpeed: '3-5s',
      uptime: '95%',
      privacy: 'Good'
    },
    pros: [
      'Well-established service',
      'Simple interface',
      'Generally reliable'
    ],
    cons: [
      'No advanced account management',
      'Slower loading times',
      'Occasional downtime',
      'No traffic optimization',
      'Basic rate limiting'
    ]
  },
  {
    name: 'Inflact',
    isOurs: false,
    logo: '🔍',
    tagline: 'Feature Rich',
    features: {
      anonymousViewing: true,
      hdDownloads: true,
      videoPlayback: true,
      mobileOptimized: true,
      noRegistration: false,
      fastLoading: false,
      reliableUptime: false,
      smartDistribution: false,
      accountPooling: false,
      rateLimiting: false,
      circuitBreakers: false,
      healthMonitoring: false,
      concurrentUsers: '20+',
      dailyLimit: '5 (Free)',
      videoQuality: 'Standard',
      loadingSpeed: '5-8s',
      uptime: '90%',
      privacy: 'Fair'
    },
    pros: [
      'Multiple Instagram tools',
      'Additional analytics features'
    ],
    cons: [
      'Requires registration',
      'Slower performance',
      'Frequent downtime',
      'Lower video quality',
      'Privacy concerns'
    ]
  },
  {
    name: 'StoriesIG',
    isOurs: false,
    logo: '📺',
    tagline: 'Basic Viewer',
    features: {
      anonymousViewing: true,
      hdDownloads: false,
      videoPlayback: true,
      mobileOptimized: false,
      noRegistration: true,
      fastLoading: false,
      reliableUptime: false,
      smartDistribution: false,
      accountPooling: false,
      rateLimiting: false,
      circuitBreakers: false,
      healthMonitoring: false,
      concurrentUsers: '10+',
      dailyLimit: 'Unlimited*',
      videoQuality: 'Standard',
      loadingSpeed: '8-12s',
      uptime: '85%',
      privacy: 'Basic'
    },
    pros: [
      'Free to use',
      'No registration required'
    ],
    cons: [
      'Poor mobile experience',
      'No HD downloads',
      'Very slow loading',
      'Frequent outages',
      'Basic functionality only'
    ]
  }
];

const featureCategories = [
  {
    name: 'Core Features',
    features: [
      { key: 'anonymousViewing', label: 'Anonymous Viewing' },
      { key: 'hdDownloads', label: 'HD Downloads' },
      { key: 'videoPlayback', label: 'Video Playback' },
      { key: 'mobileOptimized', label: 'Mobile Optimized' },
      { key: 'noRegistration', label: 'No Registration' }
    ]
  },
  {
    name: 'Performance',
    features: [
      { key: 'fastLoading', label: 'Fast Loading' },
      { key: 'reliableUptime', label: 'Reliable Uptime' },
      { key: 'loadingSpeed', label: 'Loading Speed' },
      { key: 'uptime', label: 'Uptime' }
    ]
  },
  {
    name: 'Advanced Technology',
    features: [
      { key: 'smartDistribution', label: 'Smart Distribution' },
      { key: 'accountPooling', label: 'Account Pooling' },
      { key: 'rateLimiting', label: 'Advanced Rate Limiting' },
      { key: 'circuitBreakers', label: 'Circuit Breakers' },
      { key: 'healthMonitoring', label: 'Health Monitoring' }
    ]
  }
];

export default function ComparisonPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        <Header />
        
        <main className="max-w-7xl mx-auto">
          {/* Hero Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Instagram Story Viewer Comparison
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
              See how our advanced Instagram story viewer compares to popular alternatives like Mollygram, Inflact, and StoriesIG
            </p>
          </div>

          {/* Comparison Table */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden mb-12">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                    <th className="px-6 py-4 text-left font-semibold">Features</th>
                    {competitors.map((competitor, index) => (
                      <th key={index} className="px-6 py-4 text-center font-semibold min-w-[200px]">
                        <div className="flex flex-col items-center space-y-2">
                          <span className="text-2xl">{competitor.logo}</span>
                          <span className="font-bold">{competitor.name}</span>
                          <span className="text-sm opacity-90">{competitor.tagline}</span>
                          {competitor.isOurs && (
                            <span className="bg-yellow-400 text-yellow-900 px-2 py-1 rounded-full text-xs font-bold">
                              RECOMMENDED
                            </span>
                          )}
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {featureCategories.map((category, categoryIndex) => (
                    <>
                      <tr key={`category-${categoryIndex}`} className="bg-gray-100 dark:bg-gray-700">
                        <td colSpan={competitors.length + 1} className="px-6 py-3 font-semibold text-gray-900 dark:text-white">
                          {category.name}
                        </td>
                      </tr>
                      {category.features.map((feature, featureIndex) => (
                        <tr key={`${categoryIndex}-${featureIndex}`} className="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-750">
                          <td className="px-6 py-4 font-medium text-gray-900 dark:text-white">
                            {feature.label}
                          </td>
                          {competitors.map((competitor, compIndex) => (
                            <td key={compIndex} className="px-6 py-4 text-center">
                              {typeof competitor.features[feature.key as keyof typeof competitor.features] === 'boolean' ? (
                                competitor.features[feature.key as keyof typeof competitor.features] ? (
                                  <Check className={`h-5 w-5 mx-auto ${competitor.isOurs ? 'text-green-600' : 'text-green-500'}`} />
                                ) : (
                                  <X className="h-5 w-5 text-red-500 mx-auto" />
                                )
                              ) : (
                                <span className={`text-sm ${competitor.isOurs ? 'font-semibold text-blue-600' : 'text-gray-600 dark:text-gray-400'}`}>
                                  {competitor.features[feature.key as keyof typeof competitor.features]}
                                </span>
                              )}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Detailed Comparison Cards */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {competitors.map((competitor, index) => (
              <div key={index} className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden ${competitor.isOurs ? 'ring-2 ring-blue-500' : ''}`}>
                {competitor.isOurs && (
                  <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white text-center py-2">
                    <Star className="h-4 w-4 inline mr-1" />
                    <span className="text-sm font-bold">BEST CHOICE</span>
                  </div>
                )}
                
                <div className="p-6">
                  <div className="text-center mb-4">
                    <span className="text-3xl mb-2 block">{competitor.logo}</span>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white">{competitor.name}</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{competitor.tagline}</p>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold text-green-600 mb-2">Pros:</h4>
                      <ul className="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                        {competitor.pros.map((pro, proIndex) => (
                          <li key={proIndex} className="flex items-start">
                            <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                            {pro}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-semibold text-red-600 mb-2">Cons:</h4>
                      <ul className="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                        {competitor.cons.map((con, conIndex) => (
                          <li key={conIndex} className="flex items-start">
                            <X className="h-4 w-4 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                            {con}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Why Choose Us Section */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8 mb-12">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold mb-4">Why Choose Our Instagram Story Viewer?</h2>
              <p className="text-lg opacity-90">
                Our service combines the best features of all competitors with cutting-edge technology
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <Zap className="h-12 w-12 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">Lightning Fast</h3>
                <p className="opacity-90">Sub-2 second loading times with smart account distribution</p>
              </div>
              <div className="text-center">
                <Shield className="h-12 w-12 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">Ultra Reliable</h3>
                <p className="opacity-90">99%+ uptime with circuit breaker protection</p>
              </div>
              <div className="text-center">
                <Users className="h-12 w-12 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">High Traffic Ready</h3>
                <p className="opacity-90">Handles 100+ concurrent users seamlessly</p>
              </div>
            </div>
          </div>

          {/* CTA Section */}
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Ready to Try the Best Instagram Story Viewer?
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Experience the difference with our advanced technology and superior performance
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/"
                className="inline-flex items-center px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all"
              >
                <Eye className="h-5 w-5 mr-2" />
                Start Viewing Stories
              </a>
              <a
                href="/tutorial"
                className="inline-flex items-center px-8 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                <Download className="h-5 w-5 mr-2" />
                View Tutorial
              </a>
            </div>
          </div>
        </main>

        <Footer />
      </div>
    </div>
  );
}
