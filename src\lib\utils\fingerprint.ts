// Simple fingerprint generation utility for server-side use
// Generates basic fingerprints from request headers

import { NextRequest } from 'next/server';
import { createHash } from 'crypto';

export interface SimpleFingerprint {
  fingerprintId: string;
  userAgent: string;
  acceptLanguage: string;
  acceptEncoding: string;
  ipAddress: string;
  timestamp: number;
}

/**
 * Generate a simple fingerprint from NextRequest for rate limiting
 */
export function generateFingerprint(request: NextRequest): SimpleFingerprint {
  const userAgent = request.headers.get('user-agent') || 'unknown';
  const acceptLanguage = request.headers.get('accept-language') || 'unknown';
  const acceptEncoding = request.headers.get('accept-encoding') || 'unknown';
  const ipAddress = request.headers.get('x-forwarded-for') || 
                   request.headers.get('x-real-ip') || 
                   request.headers.get('cf-connecting-ip') ||
                   '127.0.0.1';

  // Create a hash from the combination of headers
  const fingerprintData = [
    userAgent,
    acceptLanguage,
    acceptEncoding,
    ipAddress.split(',')[0].trim() // Use first IP if multiple
  ].join('|');

  const fingerprintId = createHash('sha256')
    .update(fingerprintData)
    .digest('hex')
    .substring(0, 32); // Use first 32 characters

  return {
    fingerprintId,
    userAgent,
    acceptLanguage,
    acceptEncoding,
    ipAddress: ipAddress.split(',')[0].trim(),
    timestamp: Date.now()
  };
}

/**
 * Generate a more detailed fingerprint with additional request information
 */
export function generateDetailedFingerprint(request: NextRequest): SimpleFingerprint & {
  referer?: string;
  origin?: string;
  host?: string;
  protocol: string;
} {
  const basicFingerprint = generateFingerprint(request);
  
  return {
    ...basicFingerprint,
    referer: request.headers.get('referer') || undefined,
    origin: request.headers.get('origin') || undefined,
    host: request.headers.get('host') || undefined,
    protocol: request.url.startsWith('https') ? 'https' : 'http'
  };
}

/**
 * Generate a session-based fingerprint that's more stable
 */
export function generateSessionFingerprint(request: NextRequest, sessionId?: string): SimpleFingerprint {
  const basicFingerprint = generateFingerprint(request);
  
  if (sessionId) {
    // If we have a session ID, use it as part of the fingerprint for consistency
    const sessionFingerprintData = [
      sessionId,
      basicFingerprint.userAgent,
      basicFingerprint.ipAddress
    ].join('|');

    const sessionFingerprintId = createHash('sha256')
      .update(sessionFingerprintData)
      .digest('hex')
      .substring(0, 32);

    return {
      ...basicFingerprint,
      fingerprintId: sessionFingerprintId
    };
  }

  return basicFingerprint;
}

/**
 * Validate if a fingerprint looks legitimate
 */
export function validateFingerprint(fingerprint: SimpleFingerprint): boolean {
  // Basic validation checks
  if (!fingerprint.fingerprintId || fingerprint.fingerprintId.length < 16) {
    return false;
  }

  if (!fingerprint.userAgent || fingerprint.userAgent === 'unknown') {
    return false;
  }

  if (!fingerprint.ipAddress || fingerprint.ipAddress === '127.0.0.1') {
    // Allow localhost for development
    return process.env.NODE_ENV === 'development';
  }

  // Check for suspicious patterns
  const suspiciousPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
    /python/i,
    /requests/i
  ];

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(fingerprint.userAgent)) {
      return false;
    }
  }

  return true;
}

/**
 * Check if two fingerprints are similar (for detecting potential abuse)
 */
export function areFingerprintsSimilar(
  fp1: SimpleFingerprint, 
  fp2: SimpleFingerprint,
  threshold: number = 0.8
): boolean {
  let similarities = 0;
  let totalChecks = 0;

  // Check user agent similarity
  totalChecks++;
  if (fp1.userAgent === fp2.userAgent) {
    similarities++;
  }

  // Check IP address similarity (same subnet)
  totalChecks++;
  const ip1Parts = fp1.ipAddress.split('.');
  const ip2Parts = fp2.ipAddress.split('.');
  if (ip1Parts.length === 4 && ip2Parts.length === 4) {
    // Check if same /24 subnet
    if (ip1Parts.slice(0, 3).join('.') === ip2Parts.slice(0, 3).join('.')) {
      similarities++;
    }
  }

  // Check language similarity
  totalChecks++;
  if (fp1.acceptLanguage === fp2.acceptLanguage) {
    similarities++;
  }

  // Check encoding similarity
  totalChecks++;
  if (fp1.acceptEncoding === fp2.acceptEncoding) {
    similarities++;
  }

  return (similarities / totalChecks) >= threshold;
}

/**
 * Extract risk factors from a fingerprint
 */
export function extractRiskFactors(fingerprint: SimpleFingerprint): string[] {
  const riskFactors: string[] = [];

  // Check for suspicious user agents
  const suspiciousPatterns = [
    { pattern: /bot/i, factor: 'bot_user_agent' },
    { pattern: /crawler/i, factor: 'crawler_user_agent' },
    { pattern: /spider/i, factor: 'spider_user_agent' },
    { pattern: /scraper/i, factor: 'scraper_user_agent' },
    { pattern: /curl/i, factor: 'curl_user_agent' },
    { pattern: /wget/i, factor: 'wget_user_agent' },
    { pattern: /python/i, factor: 'python_user_agent' },
    { pattern: /requests/i, factor: 'requests_library' }
  ];

  for (const { pattern, factor } of suspiciousPatterns) {
    if (pattern.test(fingerprint.userAgent)) {
      riskFactors.push(factor);
    }
  }

  // Check for missing or suspicious headers
  if (!fingerprint.acceptLanguage || fingerprint.acceptLanguage === 'unknown') {
    riskFactors.push('missing_accept_language');
  }

  if (!fingerprint.acceptEncoding || fingerprint.acceptEncoding === 'unknown') {
    riskFactors.push('missing_accept_encoding');
  }

  // Check for localhost/development IPs in production
  if (process.env.NODE_ENV === 'production') {
    const localhostPatterns = [
      /^127\./,
      /^192\.168\./,
      /^10\./,
      /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
      /^::1$/,
      /^localhost$/i
    ];

    for (const pattern of localhostPatterns) {
      if (pattern.test(fingerprint.ipAddress)) {
        riskFactors.push('localhost_ip_in_production');
        break;
      }
    }
  }

  return riskFactors;
}
