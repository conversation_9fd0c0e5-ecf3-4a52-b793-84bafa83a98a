import { Metadata } from 'next';
import { ChevronDown, ChevronUp, Shield, Eye, Download, Clock, Users, Zap } from 'lucide-react';
import { Header } from '@/components/Header';
import { Footer } from '@/components/Footer';

export const metadata: Metadata = {
  title: 'FAQ - Instagram Story Viewer | Frequently Asked Questions',
  description: 'Get answers to common questions about our Instagram story viewer. Learn about privacy, safety, usage limits, and features.',
  keywords: 'Instagram story viewer FAQ, Instagram stories questions, story viewer help, Instagram privacy, story download questions',
  openGraph: {
    title: 'FAQ - Instagram Story Viewer',
    description: 'Get answers to common questions about our Instagram story viewer.',
    type: 'website',
  },
};

const faqData = [
  {
    category: 'General Usage',
    icon: <Eye className="h-5 w-5" />,
    questions: [
      {
        question: 'How do I view Instagram stories anonymously?',
        answer: 'Simply enter the Instagram username in our search box and click "View Stories". Our service allows you to view stories without the account owner knowing you visited their profile.'
      },
      {
        question: 'Do I need to log in to use this service?',
        answer: 'No, you don\'t need to create an account or log in. Our Instagram story viewer works completely anonymously without requiring any personal information.'
      },
      {
        question: 'Can I view private account stories?',
        answer: 'No, our service only works with public Instagram accounts. Private accounts require following approval, which would compromise anonymity.'
      },
      {
        question: 'How many stories can I view per day?',
        answer: 'Free users can view up to 3 Instagram profiles per day. This limit resets every 24 hours to ensure fair usage and system stability.'
      }
    ]
  },
  {
    category: 'Privacy & Safety',
    icon: <Shield className="h-5 w-5" />,
    questions: [
      {
        question: 'Is it safe to use this Instagram story viewer?',
        answer: 'Yes, our service is completely safe. We don\'t store your data, require login credentials, or track your activity. Your privacy is our top priority.'
      },
      {
        question: 'Will the account owner know I viewed their stories?',
        answer: 'No, our service is completely anonymous. The account owner will not receive any notification or see your view in their story analytics.'
      },
      {
        question: 'Do you store the stories I view?',
        answer: 'No, we don\'t store any stories or user data. All content is fetched in real-time and not saved on our servers for privacy protection.'
      },
      {
        question: 'Is this service legal to use?',
        answer: 'Yes, viewing public Instagram content is legal. We only access publicly available information and don\'t violate any terms of service.'
      }
    ]
  },
  {
    category: 'Features & Downloads',
    icon: <Download className="h-5 w-5" />,
    questions: [
      {
        question: 'Can I download Instagram stories?',
        answer: 'Yes, you can download both photos and videos from Instagram stories. Simply click the download button on any story item to save it to your device.'
      },
      {
        question: 'What video quality do you support?',
        answer: 'We provide the highest quality available from Instagram, including HD video downloads when available. Our smart account management ensures optimal quality retrieval.'
      },
      {
        question: 'Can I view story highlights?',
        answer: 'Yes, our service supports viewing Instagram story highlights in addition to current active stories.'
      },
      {
        question: 'Do you support video playback?',
        answer: 'Yes, we provide full video playback support with controls for play, pause, and volume adjustment directly in your browser.'
      }
    ]
  },
  {
    category: 'Technical & Performance',
    icon: <Zap className="h-5 w-5" />,
    questions: [
      {
        question: 'Why is your service faster than competitors?',
        answer: 'We use advanced smart account management with intelligent request distribution across 100+ Instagram accounts, ensuring optimal performance and reliability.'
      },
      {
        question: 'How do you handle high traffic?',
        answer: 'Our system is built for high-traffic resilience with circuit breakers, load balancing, and adaptive scaling to handle hundreds of concurrent users.'
      },
      {
        question: 'What makes your rate limiting better?',
        answer: 'We implement sophisticated per-account rate limiting with predictive health monitoring and automatic account rotation to prevent service interruptions.'
      },
      {
        question: 'Do you support mobile devices?',
        answer: 'Yes, our service is fully responsive and optimized for mobile devices, tablets, and desktop computers with a modern, user-friendly interface.'
      }
    ]
  },
  {
    category: 'Troubleshooting',
    icon: <Clock className="h-5 w-5" />,
    questions: [
      {
        question: 'Why am I getting a "rate limit exceeded" message?',
        answer: 'You\'ve reached the daily limit of 3 requests. This limit resets every 24 hours. The timer shows exactly when you can use the service again.'
      },
      {
        question: 'What if a username doesn\'t work?',
        answer: 'Make sure the username is correct and the account is public. Private accounts, deleted accounts, or incorrect usernames will not work.'
      },
      {
        question: 'Why are some stories not loading?',
        answer: 'This can happen if the account has no active stories, the stories have expired (after 24 hours), or there are temporary connectivity issues.'
      },
      {
        question: 'How do I report a problem?',
        answer: 'If you encounter any issues, please refresh the page first. Most problems are resolved by refreshing. For persistent issues, the service automatically recovers.'
      }
    ]
  },
  {
    category: 'Comparison with Competitors',
    icon: <Users className="h-5 w-5" />,
    questions: [
      {
        question: 'How do you compare to Mollygram?',
        answer: 'We offer similar reliability to Mollygram but with better performance through our smart account management system and advanced rate limiting technology.'
      },
      {
        question: 'What advantages do you have over other story viewers?',
        answer: 'Our unique smart distribution system, 100+ account pool management, predictive health monitoring, and high-traffic resilience set us apart from competitors.'
      },
      {
        question: 'Why choose your service over free alternatives?',
        answer: 'We provide superior reliability, faster loading times, better video quality, and consistent uptime through our production-ready infrastructure.'
      },
      {
        question: 'Do you offer better privacy than competitors?',
        answer: 'Yes, our service is designed with privacy-first principles, using advanced anonymization techniques and no data storage policies.'
      }
    ]
  }
];

export default function FAQPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        <Header />
        
        <main className="max-w-4xl mx-auto">
          {/* Hero Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Frequently Asked Questions
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
              Get answers to common questions about our Instagram story viewer
            </p>
          </div>

          {/* FAQ Categories */}
          <div className="space-y-8">
            {faqData.map((category, categoryIndex) => (
              <div key={categoryIndex} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
                {/* Category Header */}
                <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
                  <div className="flex items-center space-x-3">
                    {category.icon}
                    <h2 className="text-xl font-semibold">{category.category}</h2>
                  </div>
                </div>

                {/* Questions */}
                <div className="p-6 space-y-4">
                  {category.questions.map((faq, faqIndex) => (
                    <details key={faqIndex} className="group">
                      <summary className="flex items-center justify-between cursor-pointer p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                        <h3 className="font-medium text-gray-900 dark:text-white pr-4">
                          {faq.question}
                        </h3>
                        <ChevronDown className="h-5 w-5 text-gray-500 group-open:rotate-180 transition-transform flex-shrink-0" />
                      </summary>
                      <div className="mt-4 p-4 text-gray-700 dark:text-gray-300 bg-gray-25 dark:bg-gray-750 rounded-lg">
                        <p>{faq.answer}</p>
                      </div>
                    </details>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* Additional Help Section */}
          <div className="mt-12 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-8 text-center">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Still Have Questions?
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Our Instagram story viewer is designed to be simple and intuitive. Most questions are answered above, but if you need additional help, try refreshing the page or checking our tutorial guide.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/tutorial"
                className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Eye className="h-5 w-5 mr-2" />
                View Tutorial
              </a>
              <a
                href="/"
                className="inline-flex items-center px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                <Zap className="h-5 w-5 mr-2" />
                Try Story Viewer
              </a>
            </div>
          </div>

          {/* Schema Markup for SEO */}
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify({
                "@context": "https://schema.org",
                "@type": "FAQPage",
                "mainEntity": faqData.flatMap(category =>
                  category.questions.map(faq => ({
                    "@type": "Question",
                    "name": faq.question,
                    "acceptedAnswer": {
                      "@type": "Answer",
                      "text": faq.answer
                    }
                  }))
                )
              })
            }}
          />
        </main>

        <Footer />
      </div>
    </div>
  );
}
