import { NextRequest, NextResponse } from 'next/server';
import { getRequestQueueManager } from '@/lib/services/request-queue-manager';
import { getSessionPoolManager } from '@/lib/services/session-pool-manager';
import { RateLimitService } from '@/lib/services/rate-limit-service';
import { UserFingerprint } from '@/lib/utils/user-fingerprint';
import {
  handleApiError,
  validateUsername,
  createErrorResponse
} from '@/lib/middleware/error-handler';
import {
  RateLimitResponse,
  RateLimitError,
  SuspiciousActivityError
} from '@/lib/types/rate-limiting';

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  let rateLimitResponse: RateLimitResponse | null = null;

  try {
    const { searchParams } = new URL(request.url);
    const username = searchParams.get('username');
    const priority = searchParams.get('priority');
    const clientId = request.headers.get('x-client-id');
    const fingerprintHeader = request.headers.get('x-user-fingerprint');

    if (!username) {
      return createErrorResponse('Username is required', 400, 'MISSING_USERNAME');
    }

    // Validate username format
    validateUsername(username);

    // Parse user fingerprint from header
    let fingerprint: UserFingerprint | null = null;
    if (fingerprintHeader) {
      try {
        fingerprint = JSON.parse(fingerprintHeader);
      } catch (error) {
        console.warn('Invalid fingerprint header:', error);
      }
    }

    if (!fingerprint || !fingerprint.fingerprintId) {
      return createErrorResponse(
        'User fingerprint is required. Please enable JavaScript and refresh the page.',
        400,
        'MISSING_FINGERPRINT'
      );
    }

    // Check rate limiting with comprehensive fingerprinting
    const rateLimitService = RateLimitService.getInstance();
    await rateLimitService.initialize();

    try {
      rateLimitResponse = await rateLimitService.checkRateLimit(
        fingerprint,
        request,
        'stories'
      );

      if (!rateLimitResponse.allowed) {
        const headers = new Headers();
        headers.set('X-RateLimit-Limit', rateLimitResponse.dailyLimit.toString());
        headers.set('X-RateLimit-Remaining', rateLimitResponse.remaining.toString());
        headers.set('X-RateLimit-Reset', rateLimitResponse.resetTime.toString());

        if (rateLimitResponse.retryAfter) {
          headers.set('Retry-After', rateLimitResponse.retryAfter.toString());
        }

        return NextResponse.json(
          {
            error: 'Rate limit exceeded',
            code: 'RATE_LIMIT_EXCEEDED',
            message: 'You have exceeded your daily request limit. Please try again later.',
            rateLimitInfo: rateLimitResponse
          },
          { status: 429, headers }
        );
      }
    } catch (error) {
      if (error instanceof RateLimitError) {
        const headers = new Headers();
        if (error.retryAfter) {
          headers.set('Retry-After', error.retryAfter.toString());
        }
        if (error.resetTime) {
          headers.set('X-RateLimit-Reset', error.resetTime.toString());
        }

        return NextResponse.json(
          {
            error: error.message,
            code: error.code,
            retryAfter: error.retryAfter,
            resetTime: error.resetTime
          },
          { status: 429, headers }
        );
      }

      if (error instanceof SuspiciousActivityError) {
        return NextResponse.json(
          {
            error: 'Suspicious activity detected',
            code: 'SUSPICIOUS_ACTIVITY',
            message: 'Your account has been temporarily restricted due to suspicious activity.',
            riskScore: error.riskScore,
            flags: error.flags
          },
          { status: 403 }
        );
      }

      throw error; // Re-throw other errors
    }

    // Process the request using Queue Manager
    const queueManager = getRequestQueueManager();
    const sessionManager = getSessionPoolManager();

    console.log(`Processing request for ${username} (client: ${clientId}, fingerprint: ${fingerprint.fingerprintId.slice(0, 8)}...)`);

    const stories = await queueManager.addRequest(username, {
      priority: priority ? parseInt(priority) : undefined,
      clientId: clientId || undefined,
      maxRetries: 2
    });

    const processingTime = Date.now() - startTime;
    const success = stories && stories.stories && stories.stories.length > 0;

    // Update request status in rate limiting service
    try {
      await rateLimitService.updateRequestStatus(
        fingerprint.fingerprintId,
        success,
        processingTime
      );
    } catch (updateError) {
      console.warn('Failed to update request status:', updateError);
    }

    // Prepare response headers with rate limit info
    const headers = new Headers();
    if (rateLimitResponse) {
      headers.set('X-RateLimit-Limit', rateLimitResponse.dailyLimit.toString());
      headers.set('X-RateLimit-Remaining', rateLimitResponse.remaining.toString());
      headers.set('X-RateLimit-Reset', rateLimitResponse.resetTime.toString());
      headers.set('X-RateLimit-Window-Start', rateLimitResponse.windowStart.toString());
      headers.set('X-RateLimit-Window-End', rateLimitResponse.windowEnd.toString());
      headers.set('X-RateLimit-User-Tier', rateLimitResponse.userTier);
    }

    return NextResponse.json({
      success: true,
      username,
      stories: stories.stories || [],
      total_count: stories.stories?.length || 0,
      method_used: stories.method_used,
      cached: stories.cached,
      processing_time_ms: processingTime,
      queue_stats: queueManager.getQueueStatus(),
      session_stats: sessionManager.getStats(),
      timestamp: new Date().toISOString(),
      rateLimitInfo: rateLimitResponse ? {
        remaining: rateLimitResponse.remaining,
        resetTime: rateLimitResponse.resetTime,
        dailyLimit: rateLimitResponse.dailyLimit,
        userTier: rateLimitResponse.userTier
      } : undefined
    }, { headers });

  } catch (error) {
    const processingTime = Date.now() - startTime;
    console.error(`Request failed after ${processingTime}ms:`, error);

    // Update request status as failed if we have fingerprint
    if (fingerprint?.fingerprintId) {
      try {
        const rateLimitService = RateLimitService.getInstance();
        await rateLimitService.updateRequestStatus(
          fingerprint.fingerprintId,
          false,
          processingTime
        );
      } catch (updateError) {
        console.warn('Failed to update failed request status:', updateError);
      }
    }

    return handleApiError(error);
  }
}
