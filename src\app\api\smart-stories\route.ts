// Enhanced API Route with Smart Account Management
// Uses the new smart account pool management system

import { NextRequest, NextResponse } from 'next/server';
import { getSmartInstagramService } from '@/lib/services/smart-instagram-service-integration';
import { IntelligentSessionManager } from '@/lib/services/intelligent-session-manager';
import { getRateLimitService } from '@/lib/services/rate-limit-service';
import { generateFingerprint } from '@/lib/utils/fingerprint';

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    const { searchParams } = new URL(request.url);
    const username = searchParams.get('username');
    const priority = searchParams.get('priority');
    const strategy = searchParams.get('strategy') as 'conservative' | 'balanced' | 'aggressive';
    const clientId = searchParams.get('client_id');

    // Validate required parameters
    if (!username) {
      return NextResponse.json(
        { 
          error: 'Username parameter is required',
          success: false 
        },
        { status: 400 }
      );
    }

    // Validate username format
    if (!/^[a-zA-Z0-9._]{1,30}$/.test(username)) {
      return NextResponse.json(
        { 
          error: 'Invalid username format',
          success: false 
        },
        { status: 400 }
      );
    }

    // Generate user fingerprint for rate limiting
    const fingerprint = generateFingerprint(request);
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     '127.0.0.1';
    const userAgent = request.headers.get('user-agent') || '';

    console.log(`📱 Smart story request: ${username} (client: ${clientId}, fingerprint: ${fingerprint.fingerprintId.slice(0, 8)}...)`);

    // Initialize services
    const rateLimitService = getRateLimitService();
    const smartService = getSmartInstagramService(rateLimitService);

    // Check user rate limits first
    let rateLimitResponse;
    try {
      rateLimitResponse = await rateLimitService.checkRateLimit(
        fingerprint.fingerprintId,
        ipAddress,
        '/api/smart-stories',
        userAgent
      );

      if (!rateLimitResponse.allowed) {
        return NextResponse.json(
          {
            error: 'Rate limit exceeded',
            success: false,
            rate_limit_info: {
              remaining: rateLimitResponse.remaining,
              reset_time: rateLimitResponse.resetTime,
              daily_limit: rateLimitResponse.dailyLimit,
              user_tier: rateLimitResponse.userTier
            }
          },
          { 
            status: 429,
            headers: {
              'X-RateLimit-Remaining': rateLimitResponse.remaining.toString(),
              'X-RateLimit-Reset': rateLimitResponse.resetTime.toString(),
              'Retry-After': Math.ceil((rateLimitResponse.resetTime - Date.now()) / 1000).toString()
            }
          }
        );
      }
    } catch (rateLimitError) {
      console.warn('Rate limit check failed:', rateLimitError);
      // Continue without rate limiting if service fails
    }

    // Process request using smart account management
    const result = await smartService.getStories({
      username,
      clientId: clientId || undefined,
      fingerprint: fingerprint.fingerprintId,
      priority: priority ? parseInt(priority) : undefined,
      strategy: strategy || 'balanced'
    });

    // Record successful request for rate limiting
    if (rateLimitResponse) {
      try {
        await rateLimitService.recordRequest(
          fingerprint.fingerprintId,
          ipAddress,
          '/api/smart-stories',
          userAgent,
          true,
          Date.now() - startTime
        );
      } catch (error) {
        console.warn('Failed to record rate limit request:', error);
      }
    }

    // Prepare response headers
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'X-Processing-Time': `${result.processing_time_ms}ms`,
      'X-Method-Used': result.method_used,
      'X-Account-Used': result.account_used || 'unknown',
      'X-System-Load': result.system_status.pool_utilization.toString()
    };

    if (rateLimitResponse) {
      headers['X-RateLimit-Remaining'] = rateLimitResponse.remaining.toString();
      headers['X-RateLimit-Reset'] = rateLimitResponse.resetTime.toString();
      headers['X-RateLimit-Limit'] = rateLimitResponse.dailyLimit.toString();
    }

    // Enhanced response with smart distribution info
    const response = {
      success: true,
      username,
      stories: result.stories,
      total_count: result.stories.length,
      has_stories: result.hasStories,
      method_used: result.method_used,
      account_used: result.account_used,
      processing_time_ms: result.processing_time_ms,
      
      // Smart distribution information
      distribution_info: {
        account_health: result.distribution_info.account_health,
        risk_score: result.distribution_info.risk_score,
        confidence: result.distribution_info.confidence,
        reasoning: result.distribution_info.reasoning
      },
      
      // Rate limiting information
      rate_limit_info: rateLimitResponse ? {
        remaining: rateLimitResponse.remaining,
        reset_time: rateLimitResponse.resetTime,
        daily_limit: rateLimitResponse.dailyLimit,
        user_tier: rateLimitResponse.userTier
      } : undefined,
      
      // System status
      system_status: {
        pool_utilization: result.system_status.pool_utilization,
        queue_length: result.system_status.queue_length,
        circuit_breaker_state: result.system_status.circuit_breaker_state,
        healthy_accounts: result.system_status.pool_utilization > 0
      },
      
      timestamp: new Date().toISOString()
    };

    return NextResponse.json(response, { headers });

  } catch (error) {
    const processingTime = Date.now() - startTime;
    console.error('Smart story request failed:', error);

    // Record failed request for rate limiting
    const fingerprint = generateFingerprint(request);
    const ipAddress = request.headers.get('x-forwarded-for') || '127.0.0.1';
    const userAgent = request.headers.get('user-agent') || '';

    try {
      const rateLimitService = getRateLimitService();
      await rateLimitService.recordRequest(
        fingerprint.fingerprintId,
        ipAddress,
        '/api/smart-stories',
        userAgent,
        false,
        processingTime
      );
    } catch (rateLimitError) {
      console.warn('Failed to record failed rate limit request:', rateLimitError);
    }

    // Determine error type and status code
    let statusCode = 500;
    let errorMessage = 'Internal server error';

    if (error instanceof Error) {
      if (error.message.includes('Rate limited') || error.message.includes('overloaded')) {
        statusCode = 429;
        errorMessage = error.message;
      } else if (error.message.includes('not found') || error.message.includes('private')) {
        statusCode = 404;
        errorMessage = 'User not found or account is private';
      } else if (error.message.includes('timeout')) {
        statusCode = 408;
        errorMessage = 'Request timeout';
      } else {
        errorMessage = error.message;
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        processing_time_ms: processingTime,
        timestamp: new Date().toISOString()
      },
      { status: statusCode }
    );
  }
}

// Health check endpoint
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const action = body.action;

    if (action === 'health-check') {
      const rateLimitService = getRateLimitService();
      const smartService = getSmartInstagramService(rateLimitService);
      
      const healthStatus = await smartService.healthCheck();
      
      return NextResponse.json({
        success: true,
        health: healthStatus,
        timestamp: new Date().toISOString()
      });
    }

    if (action === 'system-status') {
      const rateLimitService = getRateLimitService();
      const smartService = getSmartInstagramService(rateLimitService);
      
      const systemStatus = smartService.getSystemStatus();
      
      return NextResponse.json({
        success: true,
        system: systemStatus,
        timestamp: new Date().toISOString()
      });
    }

    if (action === 'analytics') {
      const rateLimitService = getRateLimitService();
      const smartService = getSmartInstagramService(rateLimitService);
      
      const analytics = await smartService.generateAnalytics();
      
      return NextResponse.json({
        success: true,
        analytics,
        timestamp: new Date().toISOString()
      });
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Unknown action' 
      },
      { status: 400 }
    );

  } catch (error) {
    console.error('Smart stories POST request failed:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    );
  }
}

// Admin endpoint for account management
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, ...params } = body;

    // Basic authentication check (in production, use proper auth)
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.includes('admin-token')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const rateLimitService = getRateLimitService();
    const smartService = getSmartInstagramService(rateLimitService);
    const management = smartService.getManagementInterface();

    switch (action) {
      case 'refresh-unhealthy':
        await smartService.refreshUnhealthyAccounts();
        return NextResponse.json({ success: true, message: 'Unhealthy accounts refreshed' });

      case 'import-accounts':
        if (!params.filePath) {
          return NextResponse.json({ success: false, error: 'File path required' }, { status: 400 });
        }
        const importResult = await smartService.importAccounts(params.filePath, params.format);
        return NextResponse.json({ success: true, result: importResult });

      case 'export-stats':
        const stats = await smartService.exportAccountStats(params.format);
        return NextResponse.json({ success: true, data: stats });

      case 'pool-command':
        if (!params.command) {
          return NextResponse.json({ success: false, error: 'Command required' }, { status: 400 });
        }
        const commandResult = await management.configManager.executeCommand(params.command);
        return NextResponse.json({ success: true, result: commandResult });

      default:
        return NextResponse.json({ success: false, error: 'Unknown action' }, { status: 400 });
    }

  } catch (error) {
    console.error('Smart stories admin request failed:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    );
  }
}
