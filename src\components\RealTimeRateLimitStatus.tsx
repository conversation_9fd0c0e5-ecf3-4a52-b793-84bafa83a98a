'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Clock, Shield, AlertTriangle, CheckCircle, RefreshCw, Zap } from 'lucide-react';
import { useRateLimit } from '@/lib/contexts/RateLimitContext';
import { RateLimitInfo } from './RateLimitStatus';
import { UserTier } from '@/lib/types/rate-limiting';

interface RealTimeRateLimitStatusProps {
  className?: string;
  showDetails?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number; // in milliseconds
  onUpgradeClick?: () => void;
}

export function RealTimeRateLimitStatus({
  className = '',
  showDetails = true,
  autoRefresh = true,
  refreshInterval = 30000, // 30 seconds
  onUpgradeClick
}: RealTimeRateLimitStatusProps) {
  const {
    rateLimitInfo,
    isLoading,
    error,
    checkRateLimit,
    fingerprintId
  } = useRateLimit();

  const [timeUntilReset, setTimeUntilReset] = useState<string>('');
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showComingSoonTooltip, setShowComingSoonTooltip] = useState(false);

  // Update countdown timer
  useEffect(() => {
    if (!rateLimitInfo?.resetTime) return;

    const updateCountdown = () => {
      const now = Date.now();
      const timeLeft = rateLimitInfo.resetTime - now;

      if (timeLeft <= 0) {
        setTimeUntilReset('Reset available');
        return;
      }

      const hours = Math.floor(timeLeft / (1000 * 60 * 60));
      const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

      if (hours > 0) {
        setTimeUntilReset(`${hours}h ${minutes}m ${seconds}s`);
      } else if (minutes > 0) {
        setTimeUntilReset(`${minutes}m ${seconds}s`);
      } else {
        setTimeUntilReset(`${seconds}s`);
      }
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, [rateLimitInfo?.resetTime]);

  // Auto-refresh rate limit status
  useEffect(() => {
    if (!autoRefresh || !fingerprintId) return;

    const interval = setInterval(async () => {
      try {
        await checkRateLimit();
        setLastUpdated(new Date());
      } catch (error) {
        console.warn('Auto-refresh failed:', error);
      }
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fingerprintId, checkRateLimit]);

  // Manual refresh
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    try {
      await checkRateLimit();
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Manual refresh failed:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, [checkRateLimit]);

  // Auto-refresh when reset time is reached
  useEffect(() => {
    if (!rateLimitInfo?.resetTime) return;

    const timeUntilReset = rateLimitInfo.resetTime - Date.now();
    if (timeUntilReset <= 0) return;

    const timeout = setTimeout(() => {
      handleRefresh();
    }, timeUntilReset + 1000); // Add 1 second buffer

    return () => clearTimeout(timeout);
  }, [rateLimitInfo?.resetTime, handleRefresh]);

  if (isLoading && !rateLimitInfo) {
    return (
      <div className={`bg-gray-50 dark:bg-gray-800 rounded-lg p-4 ${className}`}>
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          <span className="text-sm text-gray-600 dark:text-gray-400">
            Loading rate limit status...
          </span>
        </div>
      </div>
    );
  }

  if (error && !rateLimitInfo) {
    return (
      <div className={`bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-4 w-4 text-red-500" />
            <span className="text-sm text-red-700 dark:text-red-300">
              Failed to load rate limit status
            </span>
          </div>
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
          >
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>
    );
  }

  if (!rateLimitInfo) return null;

  const getStatusColor = () => {
    if (!rateLimitInfo.allowed) {
      return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20';
    }
    if (rateLimitInfo.remaining <= 1) {
      return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20';
    }
    return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20';
  };

  const getProgressPercentage = () => {
    const used = rateLimitInfo.dailyLimit - rateLimitInfo.remaining;
    return (used / rateLimitInfo.dailyLimit) * 100;
  };

  const getProgressColor = () => {
    if (rateLimitInfo.remaining <= 0) return 'bg-red-500';
    if (rateLimitInfo.remaining <= 1) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const getTierBadge = () => {
    const tierColors = {
      [UserTier.FREE]: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300',
      [UserTier.PREMIUM]: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300',
      [UserTier.ENTERPRISE]: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300',
      [UserTier.BLOCKED]: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300'
    };

    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${tierColors[rateLimitInfo.userTier]}`}>
        {rateLimitInfo.userTier.toUpperCase()}
      </span>
    );
  };

  return (
    <div className={`border rounded-lg p-4 ${getStatusColor()} ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          {rateLimitInfo.allowed ? (
            <CheckCircle className="h-5 w-5 text-green-500" />
          ) : (
            <AlertTriangle className="h-5 w-5 text-red-500" />
          )}
          <span className="font-medium text-gray-900 dark:text-white">
            Rate Limit Status
          </span>
          <div className="flex items-center space-x-2">
            {getTierBadge()}
            <button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              title="Refresh status"
            >
              <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            </button>
          </div>
        </div>
      </div>

      {/* Main Status */}
      <div className="space-y-3">
        {/* Requests Remaining */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600 dark:text-gray-400">
            Requests remaining today:
          </span>
          <span className={`font-bold text-lg ${
            rateLimitInfo.remaining <= 0 ? 'text-red-600' :
            rateLimitInfo.remaining <= 1 ? 'text-yellow-600' : 'text-green-600'
          }`}>
            {rateLimitInfo.remaining} / {rateLimitInfo.dailyLimit}
          </span>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-500 ${getProgressColor()}`}
            style={{ width: `${getProgressPercentage()}%` }}
          ></div>
        </div>

        {/* Reset Timer */}
        {rateLimitInfo.resetTime && (
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-1">
              <Clock className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Resets in:
              </span>
            </div>
            <span className="font-mono text-sm font-medium">
              {timeUntilReset}
            </span>
          </div>
        )}

        {/* Last Updated */}
        {lastUpdated && (
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </div>
        )}

        {/* Upgrade Prompt */}
        {rateLimitInfo.userTier === UserTier.FREE && rateLimitInfo.remaining <= 1 && onUpgradeClick && (
          <div className="mt-3 p-3 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-800 dark:text-blue-200 font-medium">
                  Running low on requests?
                </p>
                <p className="text-xs text-blue-600 dark:text-blue-300">
                  Upgrade for 50+ requests per day
                </p>
              </div>
              <div className="relative">
                <button
                  onClick={() => {
                    setShowComingSoonTooltip(true);
                    setTimeout(() => setShowComingSoonTooltip(false), 3000);
                  }}
                  className="flex items-center space-x-1 px-3 py-1 bg-gray-400 text-gray-600 text-xs rounded-md cursor-not-allowed opacity-75"
                  title="Coming Soon"
                >
                  <Zap className="h-3 w-3" />
                  <span>Coming Soon</span>
                </button>

                {/* Coming Soon Tooltip */}
                {showComingSoonTooltip && (
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded shadow-lg z-10 whitespace-nowrap">
                    Upgrade feature coming soon!
                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-900"></div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Details */}
        {showDetails && (
          <details className="mt-3">
            <summary className="text-xs text-gray-500 cursor-pointer hover:text-gray-700 dark:hover:text-gray-300">
              Show technical details
            </summary>
            <div className="mt-2 text-xs text-gray-600 dark:text-gray-400 space-y-1 bg-gray-50 dark:bg-gray-800 p-2 rounded">
              <div>Window: {new Date(rateLimitInfo.windowStart).toLocaleString()} - {new Date(rateLimitInfo.windowEnd).toLocaleString()}</div>
              <div>User tier: {rateLimitInfo.userTier}</div>
              <div>Fingerprint: {fingerprintId?.slice(0, 8)}...</div>
              {autoRefresh && <div>Auto-refresh: Every {refreshInterval / 1000}s</div>}
            </div>
          </details>
        )}
      </div>
    </div>
  );
}

export default RealTimeRateLimitStatus;
