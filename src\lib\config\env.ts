export const config = {
  adminApiKey: process.env.ADMIN_API_KEY || 'admin123',
  nodeEnv: process.env.NODE_ENV || 'development',
  headlessMode: process.env.HEADLESS_MODE !== 'false',
  debugMode: process.env.DEBUG === 'true',
  
  // Instagram sessions (optional)
  instagramSessions: [
    process.env.INSTAGRAM_SESSION_1,
    process.env.INSTAGRAM_SESSION_2,
    process.env.INSTAGRAM_SESSION_3,
  ].filter(Boolean),
  
  // Rate limiting configuration
  rateLimiting: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '********'), // 24 hours
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '3'),
    skipSuccessfulRequests: process.env.RATE_LIMIT_SKIP_SUCCESS === 'true',
  },
  
  // Account management
  accountManagement: {
    maxAccounts: parseInt(process.env.MAX_ACCOUNTS || '100'),
    defaultCooldownMinutes: parseInt(process.env.DEFAULT_COOLDOWN_MINUTES || '30'),
    healthCheckIntervalMinutes: parseInt(process.env.HEALTH_CHECK_INTERVAL || '5'),
  },
  
  // Browser automation
  browserAutomation: {
    headless: process.env.BROWSER_HEADLESS !== 'false',
    timeout: parseInt(process.env.BROWSER_TIMEOUT || '30000'),
    userAgent: process.env.BROWSER_USER_AGENT || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
  },
  
  // Security
  security: {
    enableFingerprinting: process.env.ENABLE_FINGERPRINTING !== 'false',
    enableRateLimiting: process.env.ENABLE_RATE_LIMITING !== 'false',
    enableSessionValidation: process.env.ENABLE_SESSION_VALIDATION !== 'false',
  },
  
  // Validate required environment variables
  validate() {
    const required: string[] = [];
    const missing = required.filter(key => !process.env[key]);
    
    if (missing.length > 0) {
      throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
    }
    
    // Validate numeric values
    const numericValidations = [
      { key: 'RATE_LIMIT_WINDOW_MS', value: this.rateLimiting.windowMs },
      { key: 'RATE_LIMIT_MAX_REQUESTS', value: this.rateLimiting.maxRequests },
      { key: 'MAX_ACCOUNTS', value: this.accountManagement.maxAccounts },
      { key: 'BROWSER_TIMEOUT', value: this.browserAutomation.timeout },
    ];
    
    for (const validation of numericValidations) {
      if (isNaN(validation.value) || validation.value <= 0) {
        throw new Error(`Invalid numeric value for ${validation.key}: ${validation.value}`);
      }
    }
    
    console.log('✅ Environment configuration validated successfully');
  },
  
  // Get configuration summary for logging
  getSummary() {
    return {
      nodeEnv: this.nodeEnv,
      headlessMode: this.headlessMode,
      debugMode: this.debugMode,
      rateLimiting: {
        windowMs: this.rateLimiting.windowMs,
        maxRequests: this.rateLimiting.maxRequests,
      },
      accountManagement: {
        maxAccounts: this.accountManagement.maxAccounts,
        healthCheckInterval: this.accountManagement.healthCheckIntervalMinutes,
      },
      security: this.security,
      instagramSessionsCount: this.instagramSessions.length,
    };
  }
};

// Validate configuration on import
try {
  config.validate();
} catch (error) {
  console.error('❌ Environment configuration error:', error);
  if (config.nodeEnv === 'production') {
    throw error;
  }
}
