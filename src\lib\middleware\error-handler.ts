import { NextResponse } from 'next/server';

export interface ApiError extends Error {
  statusCode?: number;
  code?: string;
  details?: any;
}

export class InstagramApiError extends Error implements ApiError {
  statusCode: number;
  code: string;
  details?: any;

  constructor(message: string, statusCode: number = 500, code?: string, details?: any) {
    super(message);
    this.name = 'InstagramApiError';
    this.statusCode = statusCode;
    this.code = code || 'UNKNOWN_ERROR';
    this.details = details;
  }
}

export class RateLimitError extends InstagramApiError {
  constructor(message: string = 'Rate limit exceeded', details?: any) {
    super(message, 429, 'RATE_LIMIT_EXCEEDED', details);
    this.name = 'RateLimitError';
  }
}

export class UserNotFoundError extends InstagramApiError {
  constructor(username: string) {
    super(`User '${username}' not found or account is private`, 404, 'USER_NOT_FOUND', { username });
    this.name = 'UserNotFoundError';
  }
}

export class NoStoriesError extends InstagramApiError {
  constructor(username: string) {
    super(`No stories available for user '${username}'`, 404, 'NO_STORIES', { username });
    this.name = 'NoStoriesError';
  }
}

export class ValidationError extends InstagramApiError {
  constructor(message: string, field?: string) {
    super(message, 400, 'VALIDATION_ERROR', { field });
    this.name = 'ValidationError';
  }
}

export class ServiceUnavailableError extends InstagramApiError {
  constructor(service: string, details?: any) {
    super(`Service '${service}' is currently unavailable`, 503, 'SERVICE_UNAVAILABLE', { service, ...details });
    this.name = 'ServiceUnavailableError';
  }
}

export class SuspiciousActivityError extends InstagramApiError {
  riskScore: number;
  flags: string[];

  constructor(message: string, riskScore: number, flags: string[] = []) {
    super(message, 403, 'SUSPICIOUS_ACTIVITY', { riskScore, flags });
    this.name = 'SuspiciousActivityError';
    this.riskScore = riskScore;
    this.flags = flags;
  }
}

export function handleApiError(error: unknown): NextResponse {
  console.error('API Error:', error);

  // Handle known error types
  if (error instanceof InstagramApiError) {
    return NextResponse.json(
      {
        error: error.message,
        code: error.code,
        details: error.details,
        timestamp: new Date().toISOString(),
      },
      { status: error.statusCode }
    );
  }

  // Handle axios errors
  if (error && typeof error === 'object' && 'response' in error) {
    const axiosError = error as any;
    
    if (axiosError.response?.status === 404) {
      return NextResponse.json(
        {
          error: 'User not found or account is private',
          code: 'USER_NOT_FOUND',
          timestamp: new Date().toISOString(),
        },
        { status: 404 }
      );
    }
    
    if (axiosError.response?.status === 429) {
      return NextResponse.json(
        {
          error: 'Rate limit exceeded. Please try again later.',
          code: 'RATE_LIMIT_EXCEEDED',
          timestamp: new Date().toISOString(),
        },
        { status: 429 }
      );
    }
  }

  // Handle generic errors
  if (error instanceof Error) {
    // Check for specific error messages
    if (error.message.includes('Rate limited')) {
      return NextResponse.json(
        {
          error: 'Rate limit exceeded. Please try again later.',
          code: 'RATE_LIMIT_EXCEEDED',
          timestamp: new Date().toISOString(),
        },
        { status: 429 }
      );
    }

    if (error.message.includes('User not found')) {
      return NextResponse.json(
        {
          error: 'User not found or account is private',
          code: 'USER_NOT_FOUND',
          timestamp: new Date().toISOString(),
        },
        { status: 404 }
      );
    }

    if (error.message.includes('No stories')) {
      return NextResponse.json(
        {
          error: 'No stories available for this user',
          code: 'NO_STORIES',
          timestamp: new Date().toISOString(),
        },
        { status: 404 }
      );
    }

    // Generic error response
    return NextResponse.json(
      {
        error: error.message,
        code: 'INTERNAL_ERROR',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }

  // Fallback for unknown errors
  return NextResponse.json(
    {
      error: 'An unexpected error occurred',
      code: 'UNKNOWN_ERROR',
      timestamp: new Date().toISOString(),
    },
    { status: 500 }
  );
}

export function validateUsername(username: string): void {
  if (!username || typeof username !== 'string') {
    throw new ValidationError('Username is required', 'username');
  }

  if (username.length < 1 || username.length > 30) {
    throw new ValidationError('Username must be between 1 and 30 characters', 'username');
  }

  const usernameRegex = /^[a-zA-Z0-9._]{1,30}$/;
  if (!usernameRegex.test(username)) {
    throw new ValidationError(
      'Username can only contain letters, numbers, dots, and underscores',
      'username'
    );
  }
}

export function createErrorResponse(
  message: string,
  statusCode: number = 500,
  code?: string,
  details?: any
): NextResponse {
  return NextResponse.json(
    {
      error: message,
      code: code || 'ERROR',
      details,
      timestamp: new Date().toISOString(),
    },
    { status: statusCode }
  );
}

// Utility function to check if an error is retryable
export function isRetryableError(error: unknown): boolean {
  if (error instanceof InstagramApiError) {
    // Don't retry client errors (4xx) except for rate limits
    if (error.statusCode >= 400 && error.statusCode < 500) {
      return error.statusCode === 429; // Only retry rate limits
    }
    // Retry server errors (5xx)
    return error.statusCode >= 500;
  }

  if (error && typeof error === 'object' && 'response' in error) {
    const axiosError = error as any;
    const status = axiosError.response?.status;
    
    if (status) {
      return status >= 500 || status === 429;
    }
  }

  // Retry network errors and timeouts
  if (error instanceof Error) {
    return error.message.includes('timeout') ||
           error.message.includes('ECONNRESET') ||
           error.message.includes('ENOTFOUND') ||
           error.message.includes('network');
  }

  return false;
}
