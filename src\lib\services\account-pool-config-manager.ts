// Account Pool Configuration & Management Interface
// Configuration system for account credentials, proxy settings, and pool management

import fs from 'fs/promises';
import path from 'path';
import { SmartAccountPoolManager, AccountCredentials, ManagedAccount } from './smart-account-pool-manager';
import { AccountHealthMonitor } from './account-health-monitor';
import { EnhancedSessionManager } from './enhanced-session-manager';

export interface PoolConfiguration {
  general: {
    maxAccounts: number;
    defaultRequestsPerDay: number;
    defaultCooldownMinutes: number;
    autoRotationEnabled: boolean;
    healthCheckIntervalMinutes: number;
  };
  distribution: {
    strategy: 'balanced' | 'conservative' | 'aggressive';
    geographicSpread: boolean;
    prioritizeHealthy: boolean;
    randomizationFactor: number; // 0-1
  };
  riskManagement: {
    maxConsecutiveFailures: number;
    retirementHealthThreshold: number;
    emergencyModeThreshold: number;
    suspiciousActivityDetection: boolean;
  };
  proxies: {
    enabled: boolean;
    rotationInterval: number; // minutes
    providers: ProxyProvider[];
    validation: {
      enabled: boolean;
      timeout: number;
      testUrls: string[];
    };
  };
  monitoring: {
    alertWebhooks: string[];
    metricsRetentionDays: number;
    detailedLogging: boolean;
    performanceTracking: boolean;
  };
}

export interface ProxyProvider {
  name: string;
  type: 'http' | 'socks5';
  endpoints: string[];
  authentication?: {
    username: string;
    password: string;
  };
  countries: string[];
  enabled: boolean;
}

export interface AccountImportResult {
  successful: number;
  failed: number;
  errors: string[];
  duplicates: number;
  imported: AccountCredentials[];
}

export interface PoolManagementCommand {
  action: 'refresh' | 'retire' | 'activate' | 'validate' | 'rotate';
  accountIds?: string[];
  filters?: {
    status?: string;
    healthBelow?: number;
    lastUsedBefore?: number;
    country?: string;
  };
  options?: any;
}

export interface PoolAnalytics {
  overview: {
    totalAccounts: number;
    activeAccounts: number;
    healthyAccounts: number;
    averageHealth: number;
    totalRequests24h: number;
    successRate: number;
  };
  distribution: {
    byStatus: Record<string, number>;
    byCountry: Record<string, number>;
    byHealth: Record<string, number>;
    byUsage: Record<string, number>;
  };
  performance: {
    averageResponseTime: number;
    requestsPerHour: number[];
    errorsByType: Record<string, number>;
    topPerformers: Array<{ username: string; score: number }>;
    underperformers: Array<{ username: string; issues: string[] }>;
  };
  trends: {
    healthTrend: number[]; // Last 24 hours
    usageTrend: number[];
    errorTrend: number[];
  };
}

export class AccountPoolConfigManager {
  private accountPool: SmartAccountPoolManager;
  private healthMonitor: AccountHealthMonitor;
  private sessionManager: EnhancedSessionManager;
  private config: PoolConfiguration;
  private readonly configPath = path.join(process.cwd(), 'data', 'pool-config.json');
  private readonly credentialsPath = path.join(process.cwd(), 'data', 'credentials');
  private readonly analyticsPath = path.join(process.cwd(), 'data', 'analytics.json');

  constructor(
    accountPool: SmartAccountPoolManager,
    healthMonitor: AccountHealthMonitor,
    sessionManager: EnhancedSessionManager
  ) {
    this.accountPool = accountPool;
    this.healthMonitor = healthMonitor;
    this.sessionManager = sessionManager;
    this.config = this.getDefaultConfiguration();
    this.initialize();
  }

  private getDefaultConfiguration(): PoolConfiguration {
    return {
      general: {
        maxAccounts: 100,
        defaultRequestsPerDay: 50,
        defaultCooldownMinutes: 30,
        autoRotationEnabled: true,
        healthCheckIntervalMinutes: 5
      },
      distribution: {
        strategy: 'balanced',
        geographicSpread: true,
        prioritizeHealthy: true,
        randomizationFactor: 0.2
      },
      riskManagement: {
        maxConsecutiveFailures: 5,
        retirementHealthThreshold: 30,
        emergencyModeThreshold: 80,
        suspiciousActivityDetection: true
      },
      proxies: {
        enabled: false,
        rotationInterval: 60,
        providers: [],
        validation: {
          enabled: true,
          timeout: 10000,
          testUrls: ['https://httpbin.org/ip', 'https://api.ipify.org']
        }
      },
      monitoring: {
        alertWebhooks: [],
        metricsRetentionDays: 30,
        detailedLogging: false,
        performanceTracking: true
      }
    };
  }

  private async initialize() {
    await this.ensureDirectories();
    await this.loadConfiguration();
    console.log('⚙️ Account Pool Config Manager initialized');
  }

  private async ensureDirectories() {
    const dirs = [
      path.dirname(this.configPath),
      this.credentialsPath,
      path.dirname(this.analyticsPath)
    ];

    for (const dir of dirs) {
      try {
        await fs.access(dir);
      } catch {
        await fs.mkdir(dir, { recursive: true });
      }
    }
  }

  private async loadConfiguration() {
    try {
      const configData = await fs.readFile(this.configPath, 'utf-8');
      this.config = { ...this.config, ...JSON.parse(configData) };
      console.log('📋 Configuration loaded successfully');
    } catch (error) {
      console.log('📝 No existing configuration found, using defaults');
      await this.saveConfiguration();
    }
  }

  private async saveConfiguration() {
    try {
      await fs.writeFile(this.configPath, JSON.stringify(this.config, null, 2));
      console.log('💾 Configuration saved successfully');
    } catch (error) {
      console.error('Failed to save configuration:', error);
    }
  }

  /**
   * Update configuration
   */
  async updateConfiguration(updates: Partial<PoolConfiguration>): Promise<void> {
    this.config = { ...this.config, ...updates };
    await this.saveConfiguration();
    
    // Apply configuration changes
    await this.applyConfigurationChanges();
  }

  private async applyConfigurationChanges() {
    // Update account pool settings
    // Update health monitor settings
    // Update session manager settings
    console.log('🔄 Applied configuration changes');
  }

  /**
   * Import accounts from various formats
   */
  async importAccounts(
    filePath: string,
    format: 'json' | 'csv' | 'txt' = 'json'
  ): Promise<AccountImportResult> {
    const result: AccountImportResult = {
      successful: 0,
      failed: 0,
      errors: [],
      duplicates: 0,
      imported: []
    };

    try {
      const fileContent = await fs.readFile(filePath, 'utf-8');
      let credentials: AccountCredentials[] = [];

      switch (format) {
        case 'json':
          credentials = JSON.parse(fileContent);
          break;
        case 'csv':
          credentials = this.parseCSV(fileContent);
          break;
        case 'txt':
          credentials = this.parseTextFile(fileContent);
          break;
      }

      // Validate and import credentials
      for (const cred of credentials) {
        try {
          if (this.validateCredentials(cred)) {
            // Check for duplicates
            const existing = this.accountPool.getAllAccounts().find(
              acc => acc.credentials.username === cred.username
            );

            if (existing) {
              result.duplicates++;
            } else {
              result.imported.push(cred);
              result.successful++;
            }
          } else {
            result.failed++;
            result.errors.push(`Invalid credentials for ${cred.username}`);
          }
        } catch (error) {
          result.failed++;
          result.errors.push(`Error processing ${cred.username}: ${error}`);
        }
      }

      // Add to account pool
      if (result.imported.length > 0) {
        await this.accountPool.addAccountsFromFile(filePath);
      }

      console.log(`📥 Import completed: ${result.successful} successful, ${result.failed} failed, ${result.duplicates} duplicates`);

    } catch (error) {
      result.errors.push(`File processing error: ${error}`);
    }

    return result;
  }

  private parseCSV(content: string): AccountCredentials[] {
    const lines = content.trim().split('\n');
    const headers = lines[0].split(',').map(h => h.trim());
    
    return lines.slice(1).map(line => {
      const values = line.split(',').map(v => v.trim());
      const cred: any = {};
      
      headers.forEach((header, index) => {
        if (values[index]) {
          cred[header] = values[index];
        }
      });
      
      return cred as AccountCredentials;
    });
  }

  private parseTextFile(content: string): AccountCredentials[] {
    const lines = content.trim().split('\n');
    
    return lines.map(line => {
      const parts = line.split(':');
      if (parts.length >= 2) {
        return {
          username: parts[0].trim(),
          password: parts[1].trim(),
          proxy: parts[2]?.trim(),
          country: parts[3]?.trim()
        };
      }
      throw new Error(`Invalid format: ${line}`);
    });
  }

  private validateCredentials(cred: AccountCredentials): boolean {
    return !!(cred.username && cred.password && 
             cred.username.length > 0 && cred.password.length > 0);
  }

  /**
   * Export account data
   */
  async exportAccounts(
    format: 'json' | 'csv' = 'json',
    includeStats: boolean = false
  ): Promise<string> {
    const accounts = this.accountPool.getAllAccounts();
    
    if (format === 'json') {
      const exportData = accounts.map(account => ({
        username: account.credentials.username,
        status: account.status,
        health: account.health.score,
        totalRequests: account.health.totalRequests,
        successRate: account.health.totalRequests > 0 
          ? (account.health.successfulRequests / account.health.totalRequests) * 100 
          : 0,
        lastUsed: account.usage.lastUsed,
        country: account.credentials.country,
        ...(includeStats && {
          detailedStats: {
            consecutiveFailures: account.health.consecutiveFailures,
            riskFactors: account.health.riskFactors,
            dailyRequests: account.usage.dailyRequests,
            averageResponseTime: account.health.averageResponseTime
          }
        })
      }));

      return JSON.stringify(exportData, null, 2);
    } else {
      // CSV format
      const headers = ['username', 'status', 'health', 'totalRequests', 'successRate', 'lastUsed', 'country'];
      const rows = accounts.map(account => [
        account.credentials.username,
        account.status,
        account.health.score.toFixed(1),
        account.health.totalRequests,
        account.health.totalRequests > 0 
          ? ((account.health.successfulRequests / account.health.totalRequests) * 100).toFixed(1)
          : '0',
        new Date(account.usage.lastUsed).toISOString(),
        account.credentials.country || ''
      ]);

      return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
    }
  }

  /**
   * Execute pool management commands
   */
  async executeCommand(command: PoolManagementCommand): Promise<any> {
    const accounts = this.getAccountsByFilters(command.filters);
    const targetAccounts = command.accountIds 
      ? accounts.filter(acc => command.accountIds!.includes(acc.id))
      : accounts;

    console.log(`🎯 Executing ${command.action} on ${targetAccounts.length} accounts`);

    switch (command.action) {
      case 'refresh':
        return await this.refreshAccounts(targetAccounts);
      case 'retire':
        return this.retireAccounts(targetAccounts);
      case 'activate':
        return this.activateAccounts(targetAccounts);
      case 'validate':
        return await this.validateAccounts(targetAccounts);
      case 'rotate':
        return await this.rotateAccounts(targetAccounts);
      default:
        throw new Error(`Unknown command: ${command.action}`);
    }
  }

  private getAccountsByFilters(filters?: PoolManagementCommand['filters']): ManagedAccount[] {
    let accounts = this.accountPool.getAllAccounts();

    if (!filters) return accounts;

    if (filters.status) {
      accounts = accounts.filter(acc => acc.status === filters.status);
    }

    if (filters.healthBelow) {
      accounts = accounts.filter(acc => acc.health.score < filters.healthBelow!);
    }

    if (filters.lastUsedBefore) {
      accounts = accounts.filter(acc => acc.usage.lastUsed < filters.lastUsedBefore!);
    }

    if (filters.country) {
      accounts = accounts.filter(acc => acc.credentials.country === filters.country);
    }

    return accounts;
  }

  private async refreshAccounts(accounts: ManagedAccount[]): Promise<any> {
    const results = [];
    
    for (const account of accounts) {
      try {
        const result = await this.sessionManager.refreshAccount(account.id);
        results.push({ accountId: account.id, success: result.success });
      } catch (error) {
        results.push({ accountId: account.id, success: false, error: error.message });
      }
    }

    return { refreshed: results.filter(r => r.success).length, results };
  }

  private retireAccounts(accounts: ManagedAccount[]): any {
    let retired = 0;
    
    for (const account of accounts) {
      account.status = 'retired';
      retired++;
    }

    return { retired };
  }

  private activateAccounts(accounts: ManagedAccount[]): any {
    let activated = 0;
    
    for (const account of accounts) {
      if (account.health.score > 50) {
        account.status = 'active';
        activated++;
      }
    }

    return { activated };
  }

  private async validateAccounts(accounts: ManagedAccount[]): Promise<any> {
    const results = [];
    
    for (const account of accounts) {
      try {
        const result = await this.sessionManager.validateAccount(account.id);
        results.push({ 
          accountId: account.id, 
          valid: result.isValid,
          responseTime: result.responseTime
        });
      } catch (error) {
        results.push({ accountId: account.id, valid: false, error: error.message });
      }
    }

    return { validated: results.filter(r => r.valid).length, results };
  }

  private async rotateAccounts(accounts: ManagedAccount[]): Promise<any> {
    let rotated = 0;
    
    for (const account of accounts) {
      account.status = 'cooling';
      account.usage.cooldownUntil = Date.now() + (60 * 60 * 1000); // 1 hour
      rotated++;
    }

    return { rotated };
  }

  /**
   * Generate comprehensive analytics
   */
  async generateAnalytics(): Promise<PoolAnalytics> {
    const accounts = this.accountPool.getAllAccounts();
    const poolStats = this.accountPool.getPoolStats();
    const healthStats = this.healthMonitor.getHealthStats();
    const sessionStats = this.sessionManager.getSessionStats();

    const analytics: PoolAnalytics = {
      overview: {
        totalAccounts: accounts.length,
        activeAccounts: poolStats.active,
        healthyAccounts: healthStats.healthyAccounts,
        averageHealth: poolStats.averageHealth,
        totalRequests24h: poolStats.totalRequests,
        successRate: poolStats.successRate
      },
      distribution: {
        byStatus: {
          active: poolStats.active,
          cooling: poolStats.cooling,
          blocked: poolStats.blocked,
          maintenance: poolStats.maintenance,
          retired: poolStats.retired
        },
        byCountry: poolStats.countryDistribution,
        byHealth: {
          excellent: accounts.filter(a => a.health.score >= 90).length,
          good: accounts.filter(a => a.health.score >= 70 && a.health.score < 90).length,
          fair: accounts.filter(a => a.health.score >= 50 && a.health.score < 70).length,
          poor: accounts.filter(a => a.health.score < 50).length
        },
        byUsage: {
          heavy: accounts.filter(a => a.usage.dailyRequests > 30).length,
          moderate: accounts.filter(a => a.usage.dailyRequests > 10 && a.usage.dailyRequests <= 30).length,
          light: accounts.filter(a => a.usage.dailyRequests > 0 && a.usage.dailyRequests <= 10).length,
          unused: accounts.filter(a => a.usage.dailyRequests === 0).length
        }
      },
      performance: {
        averageResponseTime: accounts.reduce((sum, a) => sum + a.health.averageResponseTime, 0) / accounts.length,
        requestsPerHour: new Array(24).fill(0), // Would be populated with real data
        errorsByType: {}, // Would be populated with real error data
        topPerformers: accounts
          .sort((a, b) => b.health.score - a.health.score)
          .slice(0, 10)
          .map(a => ({ username: a.credentials.username, score: a.health.score })),
        underperformers: accounts
          .filter(a => a.health.score < 50)
          .map(a => ({ 
            username: a.credentials.username, 
            issues: a.health.riskFactors 
          }))
      },
      trends: {
        healthTrend: new Array(24).fill(0), // Would be populated with historical data
        usageTrend: new Array(24).fill(0),
        errorTrend: new Array(24).fill(0)
      }
    };

    // Save analytics
    await this.saveAnalytics(analytics);

    return analytics;
  }

  private async saveAnalytics(analytics: PoolAnalytics) {
    try {
      const analyticsData = {
        timestamp: Date.now(),
        analytics,
        metadata: {
          version: '1.0.0',
          generatedBy: 'account-pool-config-manager'
        }
      };

      await fs.writeFile(this.analyticsPath, JSON.stringify(analyticsData, null, 2));
    } catch (error) {
      console.error('Failed to save analytics:', error);
    }
  }

  /**
   * Get current configuration
   */
  getConfiguration(): PoolConfiguration {
    return { ...this.config };
  }

  /**
   * Get pool status summary
   */
  getPoolStatus() {
    const poolStats = this.accountPool.getPoolStats();
    const healthStats = this.healthMonitor.getHealthStats();
    const sessionStats = this.sessionManager.getSessionStats();

    return {
      pool: poolStats,
      health: healthStats,
      sessions: sessionStats,
      configuration: {
        maxAccounts: this.config.general.maxAccounts,
        strategy: this.config.distribution.strategy,
        autoRotation: this.config.general.autoRotationEnabled,
        proxiesEnabled: this.config.proxies.enabled
      }
    };
  }

  /**
   * Create configuration template
   */
  async createConfigurationTemplate(filePath: string) {
    const template = {
      ...this.getDefaultConfiguration(),
      _comments: {
        general: "Basic pool settings",
        distribution: "Request distribution strategy",
        riskManagement: "Risk assessment and mitigation",
        proxies: "Proxy configuration and rotation",
        monitoring: "Monitoring and alerting settings"
      }
    };

    await fs.writeFile(filePath, JSON.stringify(template, null, 2));
    console.log(`📄 Configuration template created: ${filePath}`);
  }
}
