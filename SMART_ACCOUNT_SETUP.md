# Smart Instagram Account Management System - Setup Guide

## 🚀 Quick Start

The Smart Instagram Account Management System has been successfully implemented and integrated into your application. This system provides production-ready account pool management similar to mollygram.com.

## 📋 System Overview

### Core Features
- ✅ **Smart Account Pool**: Manages ~100 Instagram accounts with intelligent rotation
- ✅ **Request Distribution**: Sophisticated algorithms for distributing requests across accounts
- ✅ **Rate Limiting**: Advanced per-account and user rate limiting with adaptive learning
- ✅ **Health Monitoring**: Real-time account health tracking with predictive analytics
- ✅ **Session Management**: Automatic cookie refresh and session validation
- ✅ **High-Traffic Resilience**: Circuit breakers and load balancing for hundreds of concurrent users

## 🔧 Setup Instructions

### 1. Account Credentials Setup

Create an account credentials file in one of these formats:

#### JSON Format (`accounts.json`)
```json
[
  {
    "username": "account1",
    "password": "password1",
    "proxy": "proxy1.example.com:8080",
    "country": "US",
    "priority": 8,
    "tags": ["premium", "verified"]
  },
  {
    "username": "account2",
    "password": "password2",
    "proxy": "proxy2.example.com:8080",
    "country": "UK",
    "priority": 6,
    "tags": ["standard"]
  }
]
```

#### CSV Format (`accounts.csv`)
```csv
username,password,proxy,country,priority,tags
account1,password1,proxy1.example.com:8080,US,8,premium;verified
account2,password2,proxy2.example.com:8080,UK,6,standard
```

#### Text Format (`accounts.txt`)
```
account1:password1:proxy1.example.com:8080:US
account2:password2:proxy2.example.com:8080:UK
```

### 2. Environment Variables

Add these environment variables to your `.env.local`:

```env
# Smart Account Management
SMART_ACCOUNTS_ENABLED=true
SMART_ACCOUNTS_FILE=accounts.json
SMART_ACCOUNTS_MAX_POOL_SIZE=100
SMART_ACCOUNTS_STRATEGY=balanced

# Rate Limiting
RATE_LIMIT_DAILY_LIMIT=3
RATE_LIMIT_BURST_LIMIT=1
RATE_LIMIT_WINDOW=********

# Monitoring
HEALTH_CHECK_INTERVAL=300000
SESSION_VALIDATION_INTERVAL=300000
ANALYTICS_RETENTION_DAYS=30

# Emergency Settings
EMERGENCY_MODE_THRESHOLD=0.9
CIRCUIT_BREAKER_THRESHOLD=10
```

### 3. Directory Structure

Ensure these directories exist:
```
data/
├── account-pool.json          # Account pool state
├── session-manager.json       # Session management data
├── analytics.json             # System analytics
├── pool-config.json          # Pool configuration
├── backups/                  # Session backups
└── credentials/              # Credential files
```

## 🎯 API Usage

### Smart Stories Endpoint

The new smart endpoint provides enhanced functionality:

```javascript
// Basic request
GET /api/smart-stories?username=target_user

// With strategy and priority
GET /api/smart-stories?username=target_user&strategy=conservative&priority=8

// With client tracking
GET /api/smart-stories?username=target_user&client_id=user123&strategy=balanced
```

### Response Format

```json
{
  "success": true,
  "username": "target_user",
  "stories": [...],
  "total_count": 5,
  "has_stories": true,
  "method_used": "smart_distribution",
  "account_used": "account1",
  "processing_time_ms": 1250,
  "distribution_info": {
    "account_health": 95.5,
    "risk_score": 15.2,
    "confidence": 88.7,
    "reasoning": [
      "Selected account: account1",
      "Health score: 95.5/100",
      "Strategy: balanced"
    ]
  },
  "rate_limit_info": {
    "remaining": 2,
    "reset_time": *************,
    "daily_limit": 3,
    "user_tier": "standard"
  },
  "system_status": {
    "pool_utilization": 0.75,
    "queue_length": 3,
    "circuit_breaker_state": "closed",
    "healthy_accounts": true
  }
}
```

### Administrative Endpoints

#### Health Check
```javascript
POST /api/smart-stories
{
  "action": "health-check"
}
```

#### System Status
```javascript
POST /api/smart-stories
{
  "action": "system-status"
}
```

#### Analytics
```javascript
POST /api/smart-stories
{
  "action": "analytics"
}
```

#### Account Management (Admin)
```javascript
PUT /api/smart-stories
Authorization: Bearer admin-token
{
  "action": "refresh-unhealthy"
}
```

## 📊 Monitoring & Analytics

### Real-time Metrics

The system provides comprehensive monitoring:

- **Account Health**: Individual account health scores and predictive analytics
- **Request Distribution**: Real-time distribution patterns and optimization
- **Rate Limiting**: Per-account and user rate limiting statistics
- **System Performance**: Response times, success rates, and error tracking
- **Traffic Analysis**: Concurrent users, queue lengths, and load patterns

### Health Monitoring

Access health information via:
- Health check endpoint: `POST /api/smart-stories {"action": "health-check"}`
- System status: `POST /api/smart-stories {"action": "system-status"}`
- Detailed analytics: `POST /api/smart-stories {"action": "analytics"}`

## ⚙️ Configuration

### Distribution Strategies

1. **Conservative**: Longer delays, maximum account protection
2. **Balanced**: Optimal balance of speed and safety (recommended)
3. **Aggressive**: Faster requests with higher risk tolerance

### Rate Limiting Tiers

- **Standard**: 3 requests per 24 hours
- **Premium**: Enhanced limits for verified users
- **Admin**: Unlimited access for administrative functions

### Account Priorities

- **1-3**: Low priority accounts (backup use)
- **4-6**: Standard accounts (regular rotation)
- **7-8**: High priority accounts (premium features)
- **9-10**: Critical accounts (emergency use only)

## 🛡️ Security Features

### Account Protection
- Intelligent cooling-off periods
- Automatic rotation on health degradation
- Predictive failure detection
- Geographic distribution

### Rate Limiting
- Browser fingerprinting
- IP-based tracking
- Suspicious activity detection
- Adaptive learning algorithms

### Risk Management
- Real-time risk assessment
- Circuit breaker patterns
- Emergency mode activation
- Automatic account retirement

## 🚨 Troubleshooting

### Common Issues

1. **No Available Accounts**
   - Check account pool status
   - Verify account credentials
   - Review health scores

2. **High Error Rates**
   - Check account health monitoring
   - Review rate limiting settings
   - Verify proxy configurations

3. **Slow Response Times**
   - Monitor system load
   - Check queue lengths
   - Review distribution strategy

### Debug Commands

```javascript
// Check system health
curl -X POST http://localhost:3000/api/smart-stories \
  -H "Content-Type: application/json" \
  -d '{"action": "health-check"}'

// Get system status
curl -X POST http://localhost:3000/api/smart-stories \
  -H "Content-Type: application/json" \
  -d '{"action": "system-status"}'
```

## 📈 Performance Optimization

### Recommended Settings

For **high-traffic** environments (100+ concurrent users):
- Strategy: `balanced`
- Pool size: 80-100 accounts
- Health check interval: 5 minutes
- Session validation: 5 minutes

For **moderate-traffic** environments (20-50 concurrent users):
- Strategy: `conservative`
- Pool size: 40-60 accounts
- Health check interval: 10 minutes
- Session validation: 10 minutes

## 🎉 Success Metrics

The system is designed to achieve:
- **99%+ Uptime**: Through intelligent failover and redundancy
- **Sub-2s Response Times**: Optimized request distribution
- **Account Longevity**: 6+ months average account lifespan
- **High Success Rates**: 95%+ successful story retrievals
- **Scalability**: Support for 100+ concurrent users

## 📞 Support

For issues or questions:
1. Check the health monitoring endpoints
2. Review system logs for error patterns
3. Verify account pool configuration
4. Test with conservative strategy first

The Smart Instagram Account Management System is now ready for production use!
