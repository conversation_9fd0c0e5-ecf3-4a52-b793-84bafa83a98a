// Intelligent Request Distribution Algorithm
// Distributes requests across Instagram accounts with sophisticated timing and behavior patterns

import { SmartAccountPoolManager, ManagedAccount } from './smart-account-pool-manager';

export interface RequestPattern {
  peakHours: number[]; // Hours of day when requests are most common
  quietHours: number[]; // Hours when requests should be minimal
  burstProbability: number; // Probability of burst requests (0-1)
  maxBurstSize: number; // Maximum requests in a burst
  humanDelayRange: [number, number]; // Min/max delay between requests (ms)
  geographicSpread: boolean; // Whether to spread requests across countries
}

export interface DistributionStrategy {
  name: string;
  description: string;
  pattern: RequestPattern;
  accountSelectionWeight: {
    health: number;
    usage: number;
    geography: number;
    timing: number;
  };
}

export interface RequestContext {
  targetUsername: string;
  clientId?: string;
  priority: number;
  timestamp: number;
  sourceIP?: string;
  userAgent?: string;
  sessionId?: string;
}

export interface DistributionResult {
  account: ManagedAccount;
  estimatedDelay: number;
  confidence: number; // 0-100, how confident we are in this selection
  reasoning: string[];
  riskAssessment: {
    level: 'low' | 'medium' | 'high';
    factors: string[];
    mitigation: string[];
  };
}

export class IntelligentRequestDistributor {
  private accountPool: SmartAccountPoolManager;
  private strategies: Map<string, DistributionStrategy> = new Map();
  private requestHistory: RequestContext[] = [];
  private readonly maxHistorySize = 10000;

  constructor(accountPool: SmartAccountPoolManager) {
    this.accountPool = accountPool;
    this.initializeStrategies();
  }

  private initializeStrategies() {
    // Conservative strategy - mimics careful human behavior
    this.strategies.set('conservative', {
      name: 'Conservative',
      description: 'Mimics very careful human behavior with long delays',
      pattern: {
        peakHours: [9, 10, 11, 14, 15, 16, 19, 20, 21],
        quietHours: [0, 1, 2, 3, 4, 5, 6, 7, 22, 23],
        burstProbability: 0.1,
        maxBurstSize: 2,
        humanDelayRange: [45000, 180000], // 45s - 3min
        geographicSpread: true
      },
      accountSelectionWeight: {
        health: 0.5,
        usage: 0.3,
        geography: 0.15,
        timing: 0.05
      }
    });

    // Balanced strategy - normal human-like behavior
    this.strategies.set('balanced', {
      name: 'Balanced',
      description: 'Balanced approach mimicking normal user behavior',
      pattern: {
        peakHours: [8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21],
        quietHours: [0, 1, 2, 3, 4, 5, 6, 7, 22, 23],
        burstProbability: 0.25,
        maxBurstSize: 4,
        humanDelayRange: [20000, 120000], // 20s - 2min
        geographicSpread: true
      },
      accountSelectionWeight: {
        health: 0.4,
        usage: 0.35,
        geography: 0.15,
        timing: 0.1
      }
    });

    // Aggressive strategy - faster but riskier
    this.strategies.set('aggressive', {
      name: 'Aggressive',
      description: 'Faster distribution with higher risk tolerance',
      pattern: {
        peakHours: [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22],
        quietHours: [0, 1, 2, 3, 4, 5, 23],
        burstProbability: 0.4,
        maxBurstSize: 6,
        humanDelayRange: [10000, 60000], // 10s - 1min
        geographicSpread: false
      },
      accountSelectionWeight: {
        health: 0.3,
        usage: 0.4,
        geography: 0.1,
        timing: 0.2
      }
    });
  }

  /**
   * Distribute a request intelligently across the account pool
   */
  async distributeRequest(
    context: RequestContext,
    strategyName: string = 'balanced'
  ): Promise<DistributionResult> {
    const strategy = this.strategies.get(strategyName);
    if (!strategy) {
      throw new Error(`Unknown distribution strategy: ${strategyName}`);
    }

    // Add to request history
    this.addToHistory(context);

    // Analyze current conditions
    const conditions = this.analyzeCurrentConditions(strategy);
    
    // Get candidate accounts
    const candidates = await this.getCandidateAccounts(context, strategy, conditions);
    
    if (candidates.length === 0) {
      throw new Error('No suitable accounts available for request distribution');
    }

    // Score and rank candidates
    const scoredCandidates = this.scoreCandidates(candidates, context, strategy, conditions);
    
    // Select best account with some randomness
    const selectedCandidate = this.selectAccount(scoredCandidates, strategy);
    
    // Calculate optimal delay
    const delay = this.calculateOptimalDelay(selectedCandidate.account, strategy, conditions);
    
    // Assess risk
    const riskAssessment = this.assessRisk(selectedCandidate.account, context, strategy);
    
    // Generate reasoning
    const reasoning = this.generateReasoning(selectedCandidate, strategy, conditions);

    return {
      account: selectedCandidate.account,
      estimatedDelay: delay,
      confidence: selectedCandidate.score,
      reasoning,
      riskAssessment
    };
  }

  private addToHistory(context: RequestContext) {
    this.requestHistory.push(context);
    
    // Keep history size manageable
    if (this.requestHistory.length > this.maxHistorySize) {
      this.requestHistory = this.requestHistory.slice(-this.maxHistorySize);
    }
  }

  private analyzeCurrentConditions(strategy: DistributionStrategy) {
    const now = new Date();
    const hour = now.getHours();
    const dayOfWeek = now.getDay(); // 0 = Sunday
    
    // Analyze recent request patterns
    const recentRequests = this.requestHistory.filter(
      req => Date.now() - req.timestamp < 60 * 60 * 1000 // Last hour
    );
    
    const recentTargets = new Set(recentRequests.map(req => req.targetUsername));
    const recentIPs = new Set(recentRequests.map(req => req.sourceIP).filter(Boolean));
    
    return {
      currentHour: hour,
      dayOfWeek,
      isPeakHour: strategy.pattern.peakHours.includes(hour),
      isQuietHour: strategy.pattern.quietHours.includes(hour),
      isWeekend: dayOfWeek === 0 || dayOfWeek === 6,
      recentRequestCount: recentRequests.length,
      uniqueTargetsRecently: recentTargets.size,
      uniqueIPsRecently: recentIPs.size,
      shouldBurst: Math.random() < strategy.pattern.burstProbability
    };
  }

  private async getCandidateAccounts(
    context: RequestContext,
    strategy: DistributionStrategy,
    conditions: any
  ): Promise<ManagedAccount[]> {
    const allAccounts = this.accountPool.getAllAccounts();
    const now = Date.now();
    
    return allAccounts.filter(account => {
      // Basic availability checks
      if (account.status !== 'active') return false;
      if (account.usage.cooldownUntil > now) return false;
      if (account.health.score < 50) return false;
      
      // Check daily limits
      if (account.usage.dailyRequests >= 20) return false; // Conservative daily limit
      
      // During quiet hours, be more selective
      if (conditions.isQuietHour && account.health.score < 80) return false;
      
      // Avoid accounts that recently failed
      if (account.health.consecutiveFailures > 0) return false;
      
      // Geographic distribution during peak hours
      if (strategy.pattern.geographicSpread && conditions.isPeakHour) {
        const recentCountries = this.getRecentlyUsedCountries();
        const accountCountry = account.credentials.country || 'unknown';
        
        // Prefer accounts from less recently used countries
        if (recentCountries.length > 3 && recentCountries.includes(accountCountry)) {
          return Math.random() < 0.3; // 30% chance to use recently used country
        }
      }
      
      return true;
    });
  }

  private getRecentlyUsedCountries(): string[] {
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    const countries = new Set<string>();
    
    for (const account of this.accountPool.getAllAccounts()) {
      if (account.usage.lastUsed > oneHourAgo && account.credentials.country) {
        countries.add(account.credentials.country);
      }
    }
    
    return Array.from(countries);
  }

  private scoreCandidates(
    candidates: ManagedAccount[],
    context: RequestContext,
    strategy: DistributionStrategy,
    conditions: any
  ) {
    return candidates.map(account => {
      let score = 0;
      const weights = strategy.accountSelectionWeight;
      
      // Health score component
      score += account.health.score * weights.health;
      
      // Usage balance component
      const usageRatio = account.usage.dailyRequests / 20; // Max 20 requests per day
      const usageScore = Math.max(0, 100 - (usageRatio * 100));
      score += usageScore * weights.usage;
      
      // Geographic component
      const recentCountries = this.getRecentlyUsedCountries();
      const accountCountry = account.credentials.country || 'unknown';
      const geoScore = recentCountries.includes(accountCountry) ? 30 : 100;
      score += geoScore * weights.geography;
      
      // Timing component
      const timeSinceLastUse = Date.now() - account.usage.lastUsed;
      const hoursSinceLastUse = timeSinceLastUse / (60 * 60 * 1000);
      const timingScore = Math.min(100, hoursSinceLastUse * 20); // Better score for longer rest
      score += timingScore * weights.timing;
      
      // Bonus for high-priority accounts
      if (account.credentials.priority && account.credentials.priority > 7) {
        score += 10;
      }
      
      // Penalty for risk factors
      score -= account.health.riskFactors.length * 5;
      
      return {
        account,
        score: Math.max(0, Math.min(100, score))
      };
    }).sort((a, b) => b.score - a.score);
  }

  private selectAccount(scoredCandidates: any[], strategy: DistributionStrategy) {
    // Use weighted random selection from top candidates
    const topCandidates = scoredCandidates.slice(0, Math.min(5, scoredCandidates.length));
    
    if (topCandidates.length === 1) {
      return topCandidates[0];
    }
    
    // Apply exponential weighting to favor higher scores
    const weights = topCandidates.map((candidate, index) => {
      return Math.pow(2, topCandidates.length - index - 1);
    });
    
    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
    let random = Math.random() * totalWeight;
    
    for (let i = 0; i < weights.length; i++) {
      random -= weights[i];
      if (random <= 0) {
        return topCandidates[i];
      }
    }
    
    return topCandidates[0];
  }

  private calculateOptimalDelay(
    account: ManagedAccount,
    strategy: DistributionStrategy,
    conditions: any
  ): number {
    const [minDelay, maxDelay] = strategy.pattern.humanDelayRange;
    
    // Base delay with randomness
    let delay = minDelay + Math.random() * (maxDelay - minDelay);
    
    // Adjust based on conditions
    if (conditions.isQuietHour) {
      delay *= 1.5; // Longer delays during quiet hours
    }
    
    if (conditions.isPeakHour && conditions.recentRequestCount > 10) {
      delay *= 1.2; // Slightly longer delays during busy periods
    }
    
    // Account-specific adjustments
    if (account.health.score < 70) {
      delay *= 1.3; // More cautious with lower health accounts
    }
    
    if (account.usage.dailyRequests > 15) {
      delay *= 1.4; // More cautious with heavily used accounts
    }
    
    // Add some natural variation
    const variation = 0.2; // ±20% variation
    delay *= (1 + (Math.random() - 0.5) * variation);
    
    return Math.round(delay);
  }

  private assessRisk(
    account: ManagedAccount,
    context: RequestContext,
    strategy: DistributionStrategy
  ) {
    const riskFactors: string[] = [];
    const mitigation: string[] = [];
    
    // Account health risks
    if (account.health.score < 70) {
      riskFactors.push('Low account health score');
      mitigation.push('Extended cooldown period');
    }
    
    if (account.health.riskFactors.length > 0) {
      riskFactors.push(`Account has ${account.health.riskFactors.length} risk factors`);
      mitigation.push('Enhanced monitoring');
    }
    
    // Usage pattern risks
    if (account.usage.dailyRequests > 15) {
      riskFactors.push('High daily usage');
      mitigation.push('Reduced request frequency');
    }
    
    // Timing risks
    const hour = new Date().getHours();
    if (strategy.pattern.quietHours.includes(hour)) {
      riskFactors.push('Request during quiet hours');
      mitigation.push('Increased delay between requests');
    }
    
    // Determine overall risk level
    let level: 'low' | 'medium' | 'high' = 'low';
    if (riskFactors.length >= 3) {
      level = 'high';
    } else if (riskFactors.length >= 1) {
      level = 'medium';
    }
    
    return {
      level,
      factors: riskFactors,
      mitigation
    };
  }

  private generateReasoning(selectedCandidate: any, strategy: DistributionStrategy, conditions: any): string[] {
    const reasoning: string[] = [];
    const account = selectedCandidate.account;
    
    reasoning.push(`Selected account: ${account.credentials.username}`);
    reasoning.push(`Health score: ${account.health.score.toFixed(1)}/100`);
    reasoning.push(`Daily requests used: ${account.usage.dailyRequests}/20`);
    reasoning.push(`Strategy: ${strategy.name}`);
    
    if (conditions.isPeakHour) {
      reasoning.push('Peak hour - normal activity expected');
    } else if (conditions.isQuietHour) {
      reasoning.push('Quiet hour - reduced activity');
    }
    
    if (account.credentials.country) {
      reasoning.push(`Geographic location: ${account.credentials.country}`);
    }
    
    if (account.credentials.priority && account.credentials.priority > 7) {
      reasoning.push('High-priority account selected');
    }
    
    return reasoning;
  }

  /**
   * Get distribution statistics
   */
  getDistributionStats() {
    const recentRequests = this.requestHistory.filter(
      req => Date.now() - req.timestamp < 24 * 60 * 60 * 1000 // Last 24 hours
    );
    
    const hourlyDistribution = new Array(24).fill(0);
    const targetDistribution: Record<string, number> = {};
    
    for (const request of recentRequests) {
      const hour = new Date(request.timestamp).getHours();
      hourlyDistribution[hour]++;
      
      targetDistribution[request.targetUsername] = 
        (targetDistribution[request.targetUsername] || 0) + 1;
    }
    
    return {
      totalRequests24h: recentRequests.length,
      hourlyDistribution,
      targetDistribution,
      averageRequestsPerHour: recentRequests.length / 24,
      peakHour: hourlyDistribution.indexOf(Math.max(...hourlyDistribution)),
      quietestHour: hourlyDistribution.indexOf(Math.min(...hourlyDistribution))
    };
  }
}
