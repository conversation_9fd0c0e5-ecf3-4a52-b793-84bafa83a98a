import { <PERSON>ada<PERSON> } from 'next';
import { ArrowRight, Search, Eye, Download, Play, Shield, Clock, CheckCircle } from 'lucide-react';
import { Header } from '@/components/Header';
import { Footer } from '@/components/Footer';

export const metadata: Metadata = {
  title: 'How to Use Instagram Story Viewer | Step-by-Step Tutorial',
  description: 'Learn how to view and download Instagram stories anonymously with our easy step-by-step tutorial. Complete guide for beginners.',
  keywords: 'Instagram story viewer tutorial, how to view Instagram stories, Instagram story downloader guide, anonymous Instagram viewing',
  openGraph: {
    title: 'How to Use Instagram Story Viewer - Complete Tutorial',
    description: 'Easy step-by-step guide to viewing and downloading Instagram stories anonymously.',
    type: 'website',
  },
};

const tutorialSteps = [
  {
    step: 1,
    title: 'Enter Instagram Username',
    description: 'Type the Instagram username (without @) of the account whose stories you want to view',
    icon: <Search className="h-8 w-8" />,
    details: [
      'Make sure the username is spelled correctly',
      'Don\'t include the @ symbol',
      'The account must be public (private accounts won\'t work)',
      'Example: "instagram" not "@instagram"'
    ],
    tips: [
      'Double-check spelling to avoid errors',
      'Copy-paste usernames to ensure accuracy'
    ]
  },
  {
    step: 2,
    title: 'Click "View Stories"',
    description: 'Press the search button to start loading the Instagram stories',
    icon: <Eye className="h-8 w-8" />,
    details: [
      'Our system will anonymously fetch the stories',
      'Loading typically takes 1-3 seconds',
      'You\'ll see a loading indicator while we work',
      'The account owner won\'t know you viewed their stories'
    ],
    tips: [
      'Be patient during loading',
      'Don\'t refresh the page while loading'
    ]
  },
  {
    step: 3,
    title: 'Browse Available Stories',
    description: 'View all available stories from the account in a clean, organized layout',
    icon: <Play className="h-8 w-8" />,
    details: [
      'Stories are displayed in chronological order',
      'Both photos and videos are supported',
      'Videos include full playback controls',
      'Stories show remaining time before expiration'
    ],
    tips: [
      'Use arrow keys to navigate between stories',
      'Click on videos to play/pause'
    ]
  },
  {
    step: 4,
    title: 'Download Stories (Optional)',
    description: 'Save any story photo or video to your device with one click',
    icon: <Download className="h-8 w-8" />,
    details: [
      'Click the download button on any story',
      'Photos download in highest available quality',
      'Videos download in HD when available',
      'Files are saved to your default download folder'
    ],
    tips: [
      'Check your download folder if files don\'t appear immediately',
      'Some browsers may ask for download permission'
    ]
  }
];

const safetyFeatures = [
  {
    icon: <Shield className="h-6 w-6 text-green-500" />,
    title: 'Completely Anonymous',
    description: 'The account owner will never know you viewed their stories'
  },
  {
    icon: <Clock className="h-6 w-6 text-blue-500" />,
    title: 'No Registration Required',
    description: 'Start viewing stories immediately without creating an account'
  },
  {
    icon: <CheckCircle className="h-6 w-6 text-purple-500" />,
    title: 'Privacy Protected',
    description: 'We don\'t store your data or track your viewing activity'
  }
];

export default function TutorialPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        <Header />
        
        <main className="max-w-4xl mx-auto">
          {/* Hero Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              How to Use Instagram Story Viewer
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
              Follow this simple step-by-step guide to view and download Instagram stories anonymously
            </p>
            <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-lg">
              <CheckCircle className="h-5 w-5 mr-2" />
              <span className="font-medium">100% Anonymous & Safe</span>
            </div>
          </div>

          {/* Tutorial Steps */}
          <div className="space-y-8 mb-12">
            {tutorialSteps.map((step, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
                <div className="p-6">
                  {/* Step Header */}
                  <div className="flex items-center mb-6">
                    <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full mr-4">
                      <span className="font-bold text-lg">{step.step}</span>
                    </div>
                    <div className="flex-1">
                      <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-1">
                        {step.title}
                      </h2>
                      <p className="text-gray-600 dark:text-gray-300">
                        {step.description}
                      </p>
                    </div>
                    <div className="text-blue-600 dark:text-blue-400">
                      {step.icon}
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    {/* Details */}
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white mb-3">What happens:</h3>
                      <ul className="space-y-2">
                        {step.details.map((detail, detailIndex) => (
                          <li key={detailIndex} className="flex items-start">
                            <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                            <span className="text-sm text-gray-700 dark:text-gray-300">{detail}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Tips */}
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white mb-3">Pro tips:</h3>
                      <ul className="space-y-2">
                        {step.tips.map((tip, tipIndex) => (
                          <li key={tipIndex} className="flex items-start">
                            <ArrowRight className="h-4 w-4 text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
                            <span className="text-sm text-gray-700 dark:text-gray-300">{tip}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Step Connector */}
                {index < tutorialSteps.length - 1 && (
                  <div className="flex justify-center py-4">
                    <ArrowRight className="h-6 w-6 text-gray-400" />
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Safety Features */}
          <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 border border-green-200 dark:border-green-800 rounded-lg p-8 mb-12">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white text-center mb-6">
              Why Our Service is Safe & Reliable
            </h2>
            <div className="grid md:grid-cols-3 gap-6">
              {safetyFeatures.map((feature, index) => (
                <div key={index} className="text-center">
                  <div className="flex justify-center mb-3">
                    {feature.icon}
                  </div>
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    {feature.description}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Common Issues */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 mb-12">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
              Troubleshooting Common Issues
            </h2>
            <div className="space-y-4">
              <div className="border-l-4 border-yellow-500 pl-4">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                  "Username not found" error
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Make sure the username is spelled correctly and the account is public. Private accounts cannot be viewed.
                </p>
              </div>
              <div className="border-l-4 border-blue-500 pl-4">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                  "No stories available" message
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  The account either has no active stories or all stories have expired (stories last 24 hours).
                </p>
              </div>
              <div className="border-l-4 border-red-500 pl-4">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                  "Rate limit exceeded" message
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  You've reached the daily limit of 3 requests. Wait for the timer to reset or upgrade for more requests.
                </p>
              </div>
            </div>
          </div>

          {/* Video Tutorial Placeholder */}
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg p-8 text-center mb-12">
            <Play className="h-16 w-16 mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-4">Video Tutorial Coming Soon</h2>
            <p className="text-lg opacity-90 mb-6">
              We're creating a detailed video tutorial to make the process even easier to follow
            </p>
            <div className="inline-flex items-center px-6 py-3 bg-white bg-opacity-20 rounded-lg">
              <Clock className="h-5 w-5 mr-2" />
              <span>Available Soon</span>
            </div>
          </div>

          {/* CTA Section */}
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Ready to Start Viewing Stories?
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Now that you know how it works, try our Instagram story viewer for yourself
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/"
                className="inline-flex items-center px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all"
              >
                <Eye className="h-5 w-5 mr-2" />
                Start Viewing Stories
              </a>
              <a
                href="/faq"
                className="inline-flex items-center px-8 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                <Shield className="h-5 w-5 mr-2" />
                View FAQ
              </a>
            </div>
          </div>

          {/* Schema Markup for SEO */}
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify({
                "@context": "https://schema.org",
                "@type": "HowTo",
                "name": "How to Use Instagram Story Viewer",
                "description": "Step-by-step guide to viewing Instagram stories anonymously",
                "totalTime": "PT2M",
                "supply": [
                  {
                    "@type": "HowToSupply",
                    "name": "Instagram username"
                  }
                ],
                "tool": [
                  {
                    "@type": "HowToTool",
                    "name": "Web browser"
                  }
                ],
                "step": tutorialSteps.map(step => ({
                  "@type": "HowToStep",
                  "name": step.title,
                  "text": step.description,
                  "url": `#step-${step.step}`
                }))
              })
            }}
          />
        </main>

        <Footer />
      </div>
    </div>
  );
}
