'use client';

import { useState } from 'react';
import { StoryViewer } from '@/components/StoryViewer';
import { UsernameInput } from '@/components/UsernameInput';
import { Header } from '@/components/Header';
import { Footer } from '@/components/Footer';
import { RateLimitProvider, useRateLimit } from '@/lib/contexts/RateLimitContext';
import { RealTimeRateLimitStatus } from '@/components/RealTimeRateLimitStatus';
import { RateLimitExceededModal } from '@/components/RateLimitExceededModal';
import { UpgradeModal } from '@/components/UpgradeModal';
import { UserTier } from '@/lib/types/rate-limiting';
import { Shield, Zap, Users, Download, Eye, Clock, CheckCircle, Star } from 'lucide-react';

function HomeContent() {
  const [username, setUsername] = useState<string>('');
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [showUpgradeModal, setShowUpgradeModal] = useState<boolean>(false);

  const {
    rateLimitInfo,
    isLoading: rateLimitLoading,
    showRateLimitModal,
    setShowRateLimitModal,
    onUpgradeClick,
    fingerprintId,
    refreshRateLimit
  } = useRateLimit();

  const handleSearch = (searchUsername: string) => {
    setUsername(searchUsername);
    setIsSearching(true);
  };

  const handleSearchComplete = () => {
    setIsSearching(false);
  };

  const handleUpgrade = () => {
    setShowUpgradeModal(true);
  };

  const handleUpgradeSubmit = async (tier: UserTier, billingCycle: 'monthly' | 'yearly') => {
    if (!fingerprintId) {
      alert('User fingerprint not available. Please refresh the page.');
      return;
    }

    try {
      // In a real implementation, you would collect payment information first
      const paymentToken = 'demo_payment_token'; // This would come from a payment processor

      const response = await fetch('/api/upgrade', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fingerprintId,
          targetTier: tier,
          billingCycle,
          paymentToken
        })
      });

      const result = await response.json();

      if (result.success) {
        alert(`Successfully upgraded to ${tier} tier!`);
        await refreshRateLimit(); // Refresh rate limit status
        setShowUpgradeModal(false);
      } else {
        alert(`Upgrade failed: ${result.error}`);
      }
    } catch (error) {
      console.error('Upgrade failed:', error);
      alert('Upgrade failed. Please try again.');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        <Header />

        <main className="max-w-6xl mx-auto">
          {/* Hero Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-4">
              Instagram Story Viewer
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-6">
              View and download Instagram stories anonymously with advanced smart account management
            </p>
            <p className="text-lg text-gray-500 dark:text-gray-400 mb-8">
              Powered by intelligent request distribution and high-traffic resilience technology
            </p>

            {/* Trust Indicators */}
            <div className="flex flex-wrap justify-center items-center gap-6 mb-8">
              <div className="flex items-center text-green-600 dark:text-green-400">
                <Shield className="h-5 w-5 mr-2" />
                <span className="text-sm font-medium">100% Anonymous</span>
              </div>
              <div className="flex items-center text-blue-600 dark:text-blue-400">
                <Zap className="h-5 w-5 mr-2" />
                <span className="text-sm font-medium">Lightning Fast</span>
              </div>
              <div className="flex items-center text-purple-600 dark:text-purple-400">
                <Users className="h-5 w-5 mr-2" />
                <span className="text-sm font-medium">100+ Concurrent Users</span>
              </div>
              <div className="flex items-center text-orange-600 dark:text-orange-400">
                <Download className="h-5 w-5 mr-2" />
                <span className="text-sm font-medium">HD Downloads</span>
              </div>
            </div>
          </div>

          {/* Real-Time Rate Limit Status */}
          <div className="mb-6">
            <RealTimeRateLimitStatus
              onUpgradeClick={handleUpgrade}
              className="max-w-md mx-auto"
              autoRefresh={true}
              refreshInterval={30000}
              showDetails={true}
            />
          </div>

          <UsernameInput
            onSearch={handleSearch}
            isLoading={isSearching}
          />

          {username && (
            <StoryViewer
              username={username}
              onComplete={handleSearchComplete}
            />
          )}

          {/* Features Section */}
          {!username && (
            <div className="mt-16 space-y-16">
              {/* Key Features */}
              <section className="text-center">
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                  Why Choose Our Instagram Story Viewer?
                </h2>
                <p className="text-lg text-gray-600 dark:text-gray-300 mb-12">
                  Advanced technology meets user-friendly design for the best Instagram story viewing experience
                </p>

                <div className="grid md:grid-cols-3 gap-8">
                  <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <Zap className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                      Lightning Fast Performance
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Sub-2 second loading times powered by smart account distribution across 100+ Instagram accounts with intelligent request routing.
                    </p>
                  </div>

                  <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
                    <div className="w-12 h-12 bg-gradient-to-r from-green-600 to-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <Shield className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                      Complete Privacy Protection
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      100% anonymous viewing with advanced privacy safeguards. No data storage, no tracking, no registration required.
                    </p>
                  </div>

                  <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <Users className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                      High-Traffic Resilience
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Built to handle hundreds of concurrent users with circuit breaker protection and adaptive scaling technology.
                    </p>
                  </div>
                </div>
              </section>

              {/* Technical Advantages */}
              <section className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-8">
                <div className="text-center mb-8">
                  <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                    Advanced Technology Stack
                  </h2>
                  <p className="text-lg text-gray-600 dark:text-gray-300">
                    Production-ready infrastructure similar to industry leaders like Mollygram
                  </p>
                </div>

                <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="text-center">
                    <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-3" />
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Smart Distribution</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">Intelligent request routing across account pool</p>
                  </div>
                  <div className="text-center">
                    <Clock className="h-8 w-8 text-blue-500 mx-auto mb-3" />
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Rate Limiting</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">Advanced per-account rate limiting with adaptive learning</p>
                  </div>
                  <div className="text-center">
                    <Eye className="h-8 w-8 text-purple-500 mx-auto mb-3" />
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Health Monitoring</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">Predictive account health scoring and monitoring</p>
                  </div>
                  <div className="text-center">
                    <Star className="h-8 w-8 text-orange-500 mx-auto mb-3" />
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-2">99%+ Uptime</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">Circuit breaker protection and automatic failover</p>
                  </div>
                </div>
              </section>

              {/* How It Works */}
              <section className="text-center">
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                  How It Works
                </h2>
                <p className="text-lg text-gray-600 dark:text-gray-300 mb-12">
                  Simple, fast, and completely anonymous Instagram story viewing
                </p>

                <div className="grid md:grid-cols-4 gap-6">
                  {[
                    { step: 1, title: 'Enter Username', desc: 'Type the Instagram username you want to view' },
                    { step: 2, title: 'Smart Processing', desc: 'Our system intelligently selects the best account' },
                    { step: 3, title: 'View Stories', desc: 'Browse stories with full video playback support' },
                    { step: 4, title: 'Download (Optional)', desc: 'Save photos and videos in HD quality' }
                  ].map((item, index) => (
                    <div key={index} className="relative">
                      <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">
                        {item.step}
                      </div>
                      <h3 className="font-semibold text-gray-900 dark:text-white mb-2">{item.title}</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-300">{item.desc}</p>
                      {index < 3 && (
                        <div className="hidden md:block absolute top-6 left-full w-full h-0.5 bg-gradient-to-r from-blue-300 to-purple-300 transform -translate-y-1/2"></div>
                      )}
                    </div>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8 text-center">
                <h2 className="text-3xl font-bold mb-4">Ready to View Instagram Stories?</h2>
                <p className="text-lg opacity-90 mb-6">
                  Join thousands of users who trust our advanced Instagram story viewer
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <a
                    href="/tutorial"
                    className="inline-flex items-center px-6 py-3 bg-white text-blue-600 rounded-lg hover:bg-gray-100 transition-colors font-medium"
                  >
                    <Eye className="h-5 w-5 mr-2" />
                    View Tutorial
                  </a>
                  <a
                    href="/comparison"
                    className="inline-flex items-center px-6 py-3 bg-white bg-opacity-20 text-white rounded-lg hover:bg-opacity-30 transition-colors font-medium"
                  >
                    <Star className="h-5 w-5 mr-2" />
                    Compare Features
                  </a>
                </div>
              </section>
            </div>
          )}
        </main>

        <Footer />

        {/* Rate Limit Exceeded Modal */}
        {showRateLimitModal && rateLimitInfo && (
          <RateLimitExceededModal
            isOpen={showRateLimitModal}
            onClose={() => setShowRateLimitModal(false)}
            resetTime={rateLimitInfo.resetTime}
            userTier={rateLimitInfo.userTier}
            dailyLimit={rateLimitInfo.dailyLimit}
            onUpgrade={handleUpgrade}
            reason={rateLimitInfo.error}
          />
        )}

        {/* Upgrade Modal */}
        {showUpgradeModal && (
          <UpgradeModal
            isOpen={showUpgradeModal}
            onClose={() => setShowUpgradeModal(false)}
            currentTier={rateLimitInfo?.userTier || UserTier.FREE}
            onUpgrade={handleUpgradeSubmit}
          />
        )}
      </div>
    </div>
  );
}

export default function Home() {
  return (
    <RateLimitProvider onUpgrade={() => alert('Upgrade functionality would be implemented here')}>
      <HomeContent />
    </RateLimitProvider>
  );
}
