'use client';

import { Shield, Eye, AlertTriangle, Menu, X, HelpCircle, BookOpen, BarChart3, FileText } from 'lucide-react';
import { useState } from 'react';

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const navigationItems = [
    { href: '/', label: 'Home', icon: <Eye className="w-4 h-4" /> },
    { href: '/tutorial', label: 'Tutorial', icon: <BookOpen className="w-4 h-4" /> },
    { href: '/comparison', label: 'Compare', icon: <BarChart3 className="w-4 h-4" /> },
    { href: '/faq', label: 'FAQ', icon: <HelpCircle className="w-4 h-4" /> },
    { href: '/blog', label: 'Blog', icon: <FileText className="w-4 h-4" /> },
  ];

  return (
    <header className="mb-8">
      {/* Navigation Bar */}
      <nav className="flex items-center justify-between mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        {/* Logo */}
        <div className="flex items-center gap-2">
          <Eye className="w-8 h-8 text-purple-600 dark:text-purple-400" />
          <span className="text-xl font-bold text-gray-800 dark:text-gray-200">
            Story Viewer
          </span>
        </div>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center space-x-6">
          {navigationItems.map((item) => (
            <a
              key={item.href}
              href={item.href}
              className="flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 transition-colors"
            >
              {item.icon}
              <span>{item.label}</span>
            </a>
          ))}
        </div>

        {/* Mobile Menu Button */}
        <button
          onClick={() => setIsMenuOpen(!isMenuOpen)}
          className="md:hidden p-2 text-gray-600 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400"
        >
          {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
        </button>
      </nav>

      {/* Mobile Navigation Menu */}
      {isMenuOpen && (
        <div className="md:hidden mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
          <div className="space-y-3">
            {navigationItems.map((item) => (
              <a
                key={item.href}
                href={item.href}
                className="flex items-center gap-3 p-2 text-gray-600 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                {item.icon}
                <span>{item.label}</span>
              </a>
            ))}
          </div>
        </div>
      )}

      {/* Trust Indicators */}
      <div className="text-center">
        <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-600 dark:text-gray-400">
          <div className="flex items-center gap-1">
            <Shield className="w-4 h-4 text-green-500" />
            <span>Anonymous Viewing</span>
          </div>
          <div className="flex items-center gap-1">
            <AlertTriangle className="w-4 h-4 text-yellow-500" />
            <span>Public Stories Only</span>
          </div>
        </div>
      </div>
    </header>
  );
}
