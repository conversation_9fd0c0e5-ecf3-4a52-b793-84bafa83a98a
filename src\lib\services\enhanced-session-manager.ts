// Enhanced Session Management & Auto-Authentication
// Robust session management with automatic refresh and validation

import { SmartAccountPoolManager, ManagedAccount } from './smart-account-pool-manager';
import { InstagramSessionGenerator, GeneratedSession } from './instagram-session-generator';
import axios from 'axios';
import fs from 'fs/promises';
import path from 'path';

export interface SessionValidationResult {
  isValid: boolean;
  needsRefresh: boolean;
  errorType?: string;
  responseTime: number;
  userInfo?: {
    userId: string;
    username: string;
    isPrivate: boolean;
    followerCount?: number;
  };
}

export interface SessionRefreshResult {
  success: boolean;
  newSession?: GeneratedSession;
  errorMessage?: string;
  retryAfter?: number;
}

export interface SessionManagerConfig {
  validation: {
    interval: number; // ms between validation checks
    timeout: number; // request timeout
    retryAttempts: number;
    endpoints: string[];
  };
  refresh: {
    enabled: boolean;
    threshold: number; // health score below which to refresh
    batchSize: number; // max sessions to refresh simultaneously
    cooldownPeriod: number; // ms between refresh attempts
  };
  monitoring: {
    trackUserInfo: boolean;
    detectChanges: boolean;
    alertOnSuspicious: boolean;
  };
  backup: {
    enabled: boolean;
    interval: number; // ms between backups
    maxBackups: number;
  };
}

export interface SessionEvent {
  accountId: string;
  username: string;
  eventType: 'validation' | 'refresh' | 'failure' | 'recovery';
  timestamp: number;
  details: any;
  success: boolean;
}

export class EnhancedSessionManager {
  private accountPool: SmartAccountPoolManager;
  private sessionGenerator: InstagramSessionGenerator;
  private config: SessionManagerConfig;
  private validationInterval: NodeJS.Timeout | null = null;
  private backupInterval: NodeJS.Timeout | null = null;
  private refreshQueue: Set<string> = new Set();
  private events: SessionEvent[] = [];
  private readonly maxEvents = 5000;
  private readonly dataPath = path.join(process.cwd(), 'data', 'session-manager.json');

  constructor(
    accountPool: SmartAccountPoolManager,
    config?: Partial<SessionManagerConfig>
  ) {
    this.accountPool = accountPool;
    this.sessionGenerator = new InstagramSessionGenerator();
    this.config = {
      validation: {
        interval: 300000, // 5 minutes
        timeout: 15000,
        retryAttempts: 3,
        endpoints: [
          'https://www.instagram.com/api/v1/users/web_profile_info/?username=instagram',
          'https://www.instagram.com/api/v1/accounts/edit/web_form_data/',
          'https://www.instagram.com/api/v1/web/get_ruling_for_content/'
        ]
      },
      refresh: {
        enabled: true,
        threshold: 40,
        batchSize: 3,
        cooldownPeriod: 1800000 // 30 minutes
      },
      monitoring: {
        trackUserInfo: true,
        detectChanges: true,
        alertOnSuspicious: true
      },
      backup: {
        enabled: true,
        interval: 3600000, // 1 hour
        maxBackups: 24
      },
      ...config
    };

    this.initialize();
  }

  private async initialize() {
    await this.loadSessionData();
    this.startValidation();
    
    if (this.config.backup.enabled) {
      this.startBackup();
    }

    console.log('🔐 Enhanced Session Manager initialized');
  }

  private async loadSessionData() {
    try {
      const data = await fs.readFile(this.dataPath, 'utf-8');
      const sessionData = JSON.parse(data);
      
      if (sessionData.events) {
        this.events = sessionData.events.slice(-this.maxEvents);
      }
      
      console.log(`📊 Loaded ${this.events.length} session events`);
    } catch (error) {
      console.log('📝 No existing session data found, starting fresh');
    }
  }

  private async saveSessionData() {
    try {
      const sessionData = {
        events: this.events.slice(-this.maxEvents),
        lastUpdated: Date.now(),
        metadata: {
          version: '1.0.0',
          totalAccounts: this.accountPool.getAllAccounts().length
        }
      };

      await fs.writeFile(this.dataPath, JSON.stringify(sessionData, null, 2));
    } catch (error) {
      console.error('Failed to save session data:', error);
    }
  }

  private startValidation() {
    this.validationInterval = setInterval(async () => {
      await this.performBatchValidation();
    }, this.config.validation.interval);
  }

  private startBackup() {
    this.backupInterval = setInterval(async () => {
      await this.createSessionBackup();
    }, this.config.backup.interval);
  }

  private async performBatchValidation() {
    console.log('🔍 Performing batch session validation...');

    const accounts = this.accountPool.getAllAccounts()
      .filter(account => account.status === 'active')
      .sort((a, b) => a.lastValidated - b.lastValidated); // Validate oldest first

    // Validate in small batches to avoid detection
    const batchSize = 5;
    for (let i = 0; i < Math.min(accounts.length, 20); i += batchSize) {
      const batch = accounts.slice(i, i + batchSize);
      
      const validationPromises = batch.map(account => 
        this.validateAccountSession(account)
      );

      await Promise.allSettled(validationPromises);
      
      // Add delay between batches
      if (i + batchSize < accounts.length) {
        await this.delay(10000); // 10 second delay
      }
    }

    // Process refresh queue
    if (this.config.refresh.enabled && this.refreshQueue.size > 0) {
      await this.processRefreshQueue();
    }

    await this.saveSessionData();
    console.log('✅ Batch validation completed');
  }

  private async validateAccountSession(account: ManagedAccount): Promise<SessionValidationResult> {
    const startTime = Date.now();
    
    try {
      // Try multiple validation endpoints
      for (const endpoint of this.config.validation.endpoints) {
        try {
          const result = await this.testSessionEndpoint(account, endpoint);
          
          if (result.isValid) {
            // Update account validation timestamp
            account.lastValidated = Date.now();
            
            // Log successful validation
            this.logEvent(account, 'validation', {
              endpoint,
              responseTime: result.responseTime,
              userInfo: result.userInfo
            }, true);

            // Check for suspicious changes
            if (this.config.monitoring.detectChanges && result.userInfo) {
              await this.checkForSuspiciousChanges(account, result.userInfo);
            }

            return result;
          }
        } catch (error) {
          // Try next endpoint
          continue;
        }
      }

      // All endpoints failed
      const result: SessionValidationResult = {
        isValid: false,
        needsRefresh: true,
        errorType: 'validation_failed',
        responseTime: Date.now() - startTime
      };

      // Log validation failure
      this.logEvent(account, 'validation', {
        error: 'All validation endpoints failed',
        responseTime: result.responseTime
      }, false);

      // Add to refresh queue if health is low
      if (account.health.score < this.config.refresh.threshold) {
        this.refreshQueue.add(account.id);
      }

      return result;

    } catch (error) {
      const result: SessionValidationResult = {
        isValid: false,
        needsRefresh: true,
        errorType: 'validation_error',
        responseTime: Date.now() - startTime
      };

      this.logEvent(account, 'failure', {
        error: error instanceof Error ? error.message : 'Unknown error',
        responseTime: result.responseTime
      }, false);

      return result;
    }
  }

  private async testSessionEndpoint(
    account: ManagedAccount,
    endpoint: string
  ): Promise<SessionValidationResult> {
    const startTime = Date.now();

    const response = await axios.get(endpoint, {
      headers: {
        'User-Agent': account.session.userAgent,
        'Cookie': this.buildCookieString(account.session),
        'X-CSRFToken': account.session.csrftoken,
        'X-Instagram-AJAX': '1',
        'X-Requested-With': 'XMLHttpRequest',
        'Referer': 'https://www.instagram.com/',
        'Accept': 'application/json, text/javascript, */*; q=0.01'
      },
      timeout: this.config.validation.timeout
    });

    const responseTime = Date.now() - startTime;

    if (response.status === 200) {
      let userInfo;
      
      // Extract user info if available
      if (this.config.monitoring.trackUserInfo && response.data) {
        userInfo = this.extractUserInfo(response.data, account);
      }

      return {
        isValid: true,
        needsRefresh: false,
        responseTime,
        userInfo
      };
    }

    return {
      isValid: false,
      needsRefresh: true,
      errorType: `http_${response.status}`,
      responseTime
    };
  }

  private buildCookieString(session: GeneratedSession): string {
    const cookies = [
      `sessionid=${session.sessionid}`,
      `csrftoken=${session.csrftoken}`,
      `ds_user_id=${session.ds_user_id}`
    ];

    if (session.mid) cookies.push(`mid=${session.mid}`);
    if (session.ig_did) cookies.push(`ig_did=${session.ig_did}`);

    return cookies.join('; ');
  }

  private extractUserInfo(responseData: any, account: ManagedAccount): any {
    try {
      // Handle different response formats
      if (responseData.data?.user) {
        const user = responseData.data.user;
        return {
          userId: user.id || account.session.ds_user_id,
          username: user.username || account.credentials.username,
          isPrivate: user.is_private || false,
          followerCount: user.edge_followed_by?.count
        };
      }

      if (responseData.user) {
        return {
          userId: responseData.user.id || account.session.ds_user_id,
          username: responseData.user.username || account.credentials.username,
          isPrivate: responseData.user.is_private || false,
          followerCount: responseData.user.follower_count
        };
      }

      return null;
    } catch (error) {
      console.warn('Failed to extract user info:', error);
      return null;
    }
  }

  private async checkForSuspiciousChanges(account: ManagedAccount, userInfo: any) {
    if (!this.config.monitoring.alertOnSuspicious) return;

    const previousEvents = this.events.filter(
      event => event.accountId === account.id && 
               event.eventType === 'validation' && 
               event.details.userInfo
    );

    if (previousEvents.length === 0) return;

    const lastUserInfo = previousEvents[previousEvents.length - 1].details.userInfo;

    // Check for suspicious changes
    const suspiciousChanges: string[] = [];

    if (lastUserInfo.username !== userInfo.username) {
      suspiciousChanges.push(`Username changed: ${lastUserInfo.username} → ${userInfo.username}`);
    }

    if (lastUserInfo.followerCount && userInfo.followerCount) {
      const followerChange = Math.abs(userInfo.followerCount - lastUserInfo.followerCount);
      const changePercent = (followerChange / lastUserInfo.followerCount) * 100;
      
      if (changePercent > 50) {
        suspiciousChanges.push(`Large follower change: ${followerChange} (${changePercent.toFixed(1)}%)`);
      }
    }

    if (suspiciousChanges.length > 0) {
      this.logEvent(account, 'failure', {
        suspiciousChanges,
        previousInfo: lastUserInfo,
        currentInfo: userInfo
      }, false);

      console.warn(`🚨 Suspicious changes detected for ${account.credentials.username}:`, suspiciousChanges);
    }
  }

  private async processRefreshQueue() {
    if (this.refreshQueue.size === 0) return;

    console.log(`🔄 Processing refresh queue: ${this.refreshQueue.size} accounts`);

    const accountsToRefresh = Array.from(this.refreshQueue)
      .map(id => this.accountPool.getAccount(id))
      .filter(account => account !== null)
      .slice(0, this.config.refresh.batchSize);

    this.refreshQueue.clear();

    for (const account of accountsToRefresh) {
      await this.refreshAccountSession(account!);
      
      // Add delay between refreshes
      await this.delay(30000); // 30 seconds
    }
  }

  private async refreshAccountSession(account: ManagedAccount): Promise<SessionRefreshResult> {
    console.log(`🔄 Refreshing session for ${account.credentials.username}`);

    try {
      // Generate new session
      const newSession = await this.sessionGenerator.generateSingleSession(account.credentials);

      if (newSession) {
        // Update account with new session
        const oldSession = { ...account.session };
        account.session = newSession;
        account.lastValidated = Date.now();
        account.health.consecutiveFailures = 0;
        account.status = 'active';

        // Log successful refresh
        this.logEvent(account, 'refresh', {
          oldSessionId: oldSession.sessionid.slice(-8),
          newSessionId: newSession.sessionid.slice(-8),
          refreshReason: 'low_health_score'
        }, true);

        console.log(`✅ Successfully refreshed session for ${account.credentials.username}`);

        return {
          success: true,
          newSession
        };
      } else {
        throw new Error('Failed to generate new session');
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      // Log refresh failure
      this.logEvent(account, 'failure', {
        refreshAttempt: true,
        error: errorMessage
      }, false);

      // Set account to maintenance mode
      account.status = 'maintenance';
      account.usage.cooldownUntil = Date.now() + this.config.refresh.cooldownPeriod;

      console.error(`❌ Failed to refresh session for ${account.credentials.username}:`, errorMessage);

      return {
        success: false,
        errorMessage,
        retryAfter: Date.now() + this.config.refresh.cooldownPeriod
      };
    }
  }

  private async createSessionBackup() {
    try {
      const backupData = {
        timestamp: Date.now(),
        accounts: this.accountPool.getAllAccounts().map(account => ({
          id: account.id,
          username: account.credentials.username,
          session: account.session,
          health: account.health,
          status: account.status,
          lastValidated: account.lastValidated
        })),
        events: this.events.slice(-1000) // Last 1000 events
      };

      const backupPath = path.join(
        process.cwd(),
        'data',
        'backups',
        `session-backup-${Date.now()}.json`
      );

      // Ensure backup directory exists
      await fs.mkdir(path.dirname(backupPath), { recursive: true });
      
      await fs.writeFile(backupPath, JSON.stringify(backupData, null, 2));

      // Clean old backups
      await this.cleanOldBackups();

      console.log(`💾 Session backup created: ${path.basename(backupPath)}`);

    } catch (error) {
      console.error('Failed to create session backup:', error);
    }
  }

  private async cleanOldBackups() {
    try {
      const backupDir = path.join(process.cwd(), 'data', 'backups');
      const files = await fs.readdir(backupDir);
      
      const backupFiles = files
        .filter(file => file.startsWith('session-backup-'))
        .map(file => ({
          name: file,
          path: path.join(backupDir, file),
          timestamp: parseInt(file.match(/session-backup-(\d+)\.json/)?.[1] || '0')
        }))
        .sort((a, b) => b.timestamp - a.timestamp);

      // Keep only the most recent backups
      const filesToDelete = backupFiles.slice(this.config.backup.maxBackups);
      
      for (const file of filesToDelete) {
        await fs.unlink(file.path);
      }

      if (filesToDelete.length > 0) {
        console.log(`🗑️ Cleaned ${filesToDelete.length} old backup files`);
      }

    } catch (error) {
      console.warn('Failed to clean old backups:', error);
    }
  }

  private logEvent(
    account: ManagedAccount,
    eventType: SessionEvent['eventType'],
    details: any,
    success: boolean
  ) {
    const event: SessionEvent = {
      accountId: account.id,
      username: account.credentials.username,
      eventType,
      timestamp: Date.now(),
      details,
      success
    };

    this.events.push(event);

    // Keep events manageable
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Manually refresh a specific account
   */
  async refreshAccount(accountId: string): Promise<SessionRefreshResult> {
    const account = this.accountPool.getAccount(accountId);
    if (!account) {
      return {
        success: false,
        errorMessage: 'Account not found'
      };
    }

    return await this.refreshAccountSession(account);
  }

  /**
   * Validate a specific account
   */
  async validateAccount(accountId: string): Promise<SessionValidationResult> {
    const account = this.accountPool.getAccount(accountId);
    if (!account) {
      return {
        isValid: false,
        needsRefresh: false,
        errorType: 'account_not_found',
        responseTime: 0
      };
    }

    return await this.validateAccountSession(account);
  }

  /**
   * Get session statistics
   */
  getSessionStats() {
    const accounts = this.accountPool.getAllAccounts();
    const recentEvents = this.events.filter(
      event => Date.now() - event.timestamp < 24 * 60 * 60 * 1000 // Last 24 hours
    );

    return {
      totalAccounts: accounts.length,
      validatedToday: recentEvents.filter(e => e.eventType === 'validation' && e.success).length,
      refreshedToday: recentEvents.filter(e => e.eventType === 'refresh' && e.success).length,
      failuresToday: recentEvents.filter(e => !e.success).length,
      accountsNeedingRefresh: this.refreshQueue.size,
      averageValidationAge: this.calculateAverageValidationAge(accounts),
      sessionHealth: this.calculateSessionHealth(accounts),
      recentEvents: recentEvents.slice(-20)
    };
  }

  private calculateAverageValidationAge(accounts: ManagedAccount[]): number {
    const now = Date.now();
    const ages = accounts.map(account => now - account.lastValidated);
    return ages.reduce((sum, age) => sum + age, 0) / ages.length;
  }

  private calculateSessionHealth(accounts: ManagedAccount[]): number {
    const healthScores = accounts.map(account => account.health.score);
    return healthScores.reduce((sum, score) => sum + score, 0) / healthScores.length;
  }

  /**
   * Get recent events
   */
  getRecentEvents(limit: number = 100): SessionEvent[] {
    return this.events
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }

  /**
   * Cleanup
   */
  async cleanup() {
    if (this.validationInterval) {
      clearInterval(this.validationInterval);
    }

    if (this.backupInterval) {
      clearInterval(this.backupInterval);
    }

    await this.saveSessionData();
    await this.sessionGenerator.cleanup();

    console.log('🧹 Enhanced Session Manager cleaned up');
  }
}
