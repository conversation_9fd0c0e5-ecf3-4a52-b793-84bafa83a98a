#!/usr/bin/env node

// Critical Issues Fix Script
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Fixing critical issues in Instagram Story Viewer...\n');

async function fixCriticalIssues() {
  try {
    // 1. Install missing otplib dependency
    console.log('📦 Installing missing otplib dependency...');
    execSync('npm install otplib@^12.0.1', { stdio: 'inherit' });
    console.log('✅ otplib installed successfully\n');

    // 2. Fix TypeScript configuration
    console.log('🔧 Fixing TypeScript configuration...');
    const tsConfigScripts = {
      "compilerOptions": {
        "target": "es2020",
        "module": "commonjs",
        "outDir": "./dist",
        "rootDir": "./src",
        "strict": false,
        "esModuleInterop": true,
        "allowSyntheticDefaultImports": true,
        "skipLibCheck": true,
        "forceConsistentCasingInFileNames": true,
        "moduleResolution": "node",
        "declaration": false,
        "sourceMap": false,
        "resolveJsonModule": true
      },
      "include": [
        "src/lib/services/instagram-session-generator.ts",
        "src/lib/services/smart-account-pool-manager.ts"
      ],
      "exclude": [
        "node_modules",
        ".next",
        "dist"
      ]
    };
    
    fs.writeFileSync('tsconfig.scripts.json', JSON.stringify(tsConfigScripts, null, 2));
    console.log('✅ TypeScript configuration fixed\n');

    // 3. Create environment configuration
    console.log('🌍 Creating environment configuration...');
    const envConfig = `export const config = {
  adminApiKey: process.env.ADMIN_API_KEY || 'admin123',
  nodeEnv: process.env.NODE_ENV || 'development',
  headlessMode: process.env.HEADLESS_MODE !== 'false',
  debugMode: process.env.DEBUG === 'true',
  
  // Instagram sessions (optional)
  instagramSessions: [
    process.env.INSTAGRAM_SESSION_1,
    process.env.INSTAGRAM_SESSION_2,
    process.env.INSTAGRAM_SESSION_3,
  ].filter(Boolean),
  
  // Validate required environment variables
  validate() {
    const required = [];
    const missing = required.filter(key => !process.env[key]);
    
    if (missing.length > 0) {
      throw new Error(\`Missing required environment variables: \${missing.join(', ')}\`);
    }
  }
};`;

    const configDir = path.join('src', 'lib', 'config');
    if (!fs.existsSync(configDir)) {
      fs.mkdirSync(configDir, { recursive: true });
    }
    fs.writeFileSync(path.join(configDir, 'env.ts'), envConfig);
    console.log('✅ Environment configuration created\n');

    // 4. Create error boundary component
    console.log('🛡️ Creating error boundary component...');
    const errorBoundary = `'use client';

import React from 'react';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-red-600 mb-4">Something went wrong</h2>
            <p className="text-gray-600 mb-4">Please refresh the page and try again.</p>
            <button 
              onClick={() => this.setState({ hasError: false })}
              className="px-4 py-2 bg-blue-600 text-white rounded"
            >
              Try Again
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}`;

    fs.writeFileSync(path.join('src', 'components', 'ErrorBoundary.tsx'), errorBoundary);
    console.log('✅ Error boundary component created\n');

    // 5. Create data directory if it doesn't exist
    console.log('📁 Ensuring data directory exists...');
    const dataDir = path.join(process.cwd(), 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
      console.log('✅ Data directory created');
    } else {
      console.log('✅ Data directory already exists');
    }

    // 6. Check if accounts.json exists
    const accountsPath = path.join(dataDir, 'accounts.json');
    if (fs.existsSync(accountsPath)) {
      console.log('✅ accounts.json file found');
      
      // Validate the structure
      try {
        const accounts = JSON.parse(fs.readFileSync(accountsPath, 'utf-8'));
        console.log(`📊 Found ${accounts.length} accounts in accounts.json`);
        
        const accountsWithTwoFA = accounts.filter(acc => acc.twoFactorKey).length;
        console.log(`🔐 ${accountsWithTwoFA} accounts have 2FA enabled`);
      } catch (error) {
        console.log('⚠️ Error reading accounts.json:', error.message);
      }
    } else {
      console.log('⚠️ accounts.json not found - you may need to create it');
    }

    console.log('\n🎉 Critical issues fixed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Run: npm run build');
    console.log('   2. Run: npm run dev');
    console.log('   3. Test the application');
    console.log('   4. Check browser console for any remaining errors');

  } catch (error) {
    console.error('❌ Error fixing critical issues:', error.message);
    process.exit(1);
  }
}

fixCriticalIssues();
