// Smart Instagram Service Integration
// Integrates smart account management with existing Instagram story service

import { SmartAccountPoolManager } from './smart-account-pool-manager';
import { IntelligentRequestDistributor } from './intelligent-request-distributor';
import { AdvancedRateLimiter } from './advanced-rate-limiter';
import { AccountHealthMonitor } from './account-health-monitor';
import { EnhancedSessionManager } from './enhanced-session-manager';
import { HighTrafficResilienceManager } from './high-traffic-resilience';
import { AccountPoolConfigManager } from './account-pool-config-manager';
import { RateLimitService } from './rate-limit-service';
import axios from 'axios';

export interface SmartStoryRequest {
  username: string;
  clientId?: string;
  fingerprint?: string;
  priority?: number;
  strategy?: 'conservative' | 'balanced' | 'aggressive';
}

export interface SmartStoryResponse {
  stories: any[];
  hasStories: boolean;
  method_used: string;
  account_used?: string;
  processing_time_ms: number;
  distribution_info: {
    account_health: number;
    risk_score: number;
    confidence: number;
    reasoning: string[];
  };
  rate_limit_info?: {
    remaining: number;
    reset_time: number;
    daily_limit: number;
  };
  system_status: {
    pool_utilization: number;
    queue_length: number;
    circuit_breaker_state: string;
  };
}

export class SmartInstagramServiceIntegration {
  private accountPool: SmartAccountPoolManager;
  private distributor: IntelligentRequestDistributor;
  private rateLimiter: AdvancedRateLimiter;
  private healthMonitor: AccountHealthMonitor;
  private sessionManager: EnhancedSessionManager;
  private resilienceManager: HighTrafficResilienceManager;
  private configManager: AccountPoolConfigManager;
  private userRateLimiter: RateLimitService;
  
  private isInitialized = false;

  constructor(userRateLimiter: RateLimitService) {
    this.userRateLimiter = userRateLimiter;
    this.initialize();
  }

  private async initialize() {
    try {
      console.log('🚀 Initializing Smart Instagram Service Integration...');

      // Initialize core components in dependency order
      this.accountPool = new SmartAccountPoolManager();
      this.distributor = new IntelligentRequestDistributor(this.accountPool);
      this.rateLimiter = new AdvancedRateLimiter(this.accountPool, this.userRateLimiter);
      this.healthMonitor = new AccountHealthMonitor(this.accountPool);
      this.sessionManager = new EnhancedSessionManager(this.accountPool);
      this.resilienceManager = new HighTrafficResilienceManager(
        this.accountPool,
        this.distributor,
        this.rateLimiter,
        this.healthMonitor
      );
      this.configManager = new AccountPoolConfigManager(
        this.accountPool,
        this.healthMonitor,
        this.sessionManager
      );

      // Load accounts if credentials file exists
      await this.loadAccountsIfAvailable();

      this.isInitialized = true;
      console.log('✅ Smart Instagram Service Integration initialized successfully');

    } catch (error) {
      console.error('❌ Failed to initialize Smart Instagram Service Integration:', error);
      throw error;
    }
  }

  private async loadAccountsIfAvailable() {
    try {
      // Try to load from various credential file formats
      const credentialFiles = [
        'accounts.json',
        'accounts.csv',
        'accounts.txt',
        'credentials.json'
      ];

      for (const filename of credentialFiles) {
        try {
          await this.configManager.importAccounts(filename);
          console.log(`📥 Loaded accounts from ${filename}`);
          break;
        } catch (error) {
          // Try next file
          continue;
        }
      }
    } catch (error) {
      console.log('📝 No credential files found, starting with empty pool');
    }
  }

  /**
   * Main method to fetch Instagram stories using smart distribution
   */
  async getStories(request: SmartStoryRequest): Promise<SmartStoryResponse> {
    if (!this.isInitialized) {
      throw new Error('Service not initialized');
    }

    const startTime = Date.now();

    try {
      // Use high-traffic resilience manager for request processing
      const result = await this.resilienceManager.addRequest(
        request.username,
        request.clientId || 'anonymous',
        request.fingerprint || 'unknown',
        {
          level: this.mapPriorityLevel(request.priority),
          weight: request.priority || 1,
          maxWaitTime: 60000
        }
      );

      // If resilience manager returns a result, process it
      if (result) {
        return this.formatResponse(result, startTime, request);
      }

      // Fallback to direct processing
      return await this.processRequestDirectly(request, startTime);

    } catch (error) {
      console.error('Smart story request failed:', error);
      
      // Return error response with system status
      return {
        stories: [],
        hasStories: false,
        method_used: 'error',
        processing_time_ms: Date.now() - startTime,
        distribution_info: {
          account_health: 0,
          risk_score: 100,
          confidence: 0,
          reasoning: [`Error: ${error instanceof Error ? error.message : 'Unknown error'}`]
        },
        system_status: this.getSystemStatus()
      };
    }
  }

  private mapPriorityLevel(priority?: number): 'low' | 'normal' | 'high' | 'critical' {
    if (!priority) return 'normal';
    if (priority >= 8) return 'critical';
    if (priority >= 6) return 'high';
    if (priority >= 3) return 'normal';
    return 'low';
  }

  private async processRequestDirectly(
    request: SmartStoryRequest,
    startTime: number
  ): Promise<SmartStoryResponse> {
    // Get optimal account distribution
    const distribution = await this.distributor.distributeRequest({
      targetUsername: request.username,
      clientId: request.clientId,
      priority: request.priority || 1,
      timestamp: Date.now()
    }, request.strategy || 'balanced');

    // Check rate limits
    const rateLimitResult = await this.rateLimiter.checkAccountRateLimit(
      distribution.account.id,
      request.username,
      request.fingerprint
    );

    if (!rateLimitResult.allowed) {
      throw new Error(`Rate limited: ${rateLimitResult.reason}`);
    }

    // Add recommended delay
    if (distribution.estimatedDelay > 0) {
      await this.delay(distribution.estimatedDelay);
    }

    // Execute Instagram request
    const storyResult = await this.executeInstagramRequest(
      distribution.account,
      request.username
    );

    // Record request outcome
    const responseTime = Date.now() - startTime;
    const success = storyResult.stories && storyResult.stories.length > 0;

    this.accountPool.markRequestSuccess(
      distribution.account.id,
      responseTime,
      request.username
    );

    this.rateLimiter.recordRequest(
      distribution.account.id,
      success,
      responseTime,
      request.username
    );

    return this.formatResponse(storyResult, startTime, request, distribution);
  }

  private async executeInstagramRequest(account: any, username: string): Promise<any> {
    try {
      // Use the account's session to make Instagram API requests
      const response = await this.makeInstagramAPIRequest(account, username);
      
      if (response && response.data) {
        return this.parseInstagramResponse(response.data, username);
      }

      // Fallback to web scraping if API fails
      return await this.fallbackWebScraping(account, username);

    } catch (error) {
      console.error(`Instagram request failed for ${username}:`, error);
      throw error;
    }
  }

  private async makeInstagramAPIRequest(account: any, username: string): Promise<any> {
    // Try multiple Instagram API endpoints
    const endpoints = [
      `https://www.instagram.com/api/v1/users/web_profile_info/?username=${username}`,
      `https://i.instagram.com/api/v1/users/${username}/info/`,
      `https://www.instagram.com/${username}/?__a=1`
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await axios.get(endpoint, {
          headers: {
            'User-Agent': account.session.userAgent,
            'Cookie': this.buildCookieString(account.session),
            'X-CSRFToken': account.session.csrftoken,
            'X-Instagram-AJAX': '1',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': 'https://www.instagram.com/',
            'Accept': 'application/json'
          },
          timeout: 15000
        });

        if (response.status === 200 && response.data) {
          return response;
        }
      } catch (error) {
        // Try next endpoint
        continue;
      }
    }

    throw new Error('All Instagram API endpoints failed');
  }

  private buildCookieString(session: any): string {
    const cookies = [
      `sessionid=${session.sessionid}`,
      `csrftoken=${session.csrftoken}`,
      `ds_user_id=${session.ds_user_id}`
    ];

    if (session.mid) cookies.push(`mid=${session.mid}`);
    if (session.ig_did) cookies.push(`ig_did=${session.ig_did}`);

    return cookies.join('; ');
  }

  private parseInstagramResponse(data: any, username: string): any {
    try {
      // Handle different response formats
      let user = null;

      if (data.data?.user) {
        user = data.data.user;
      } else if (data.user) {
        user = data.user;
      } else if (data.graphql?.user) {
        user = data.graphql.user;
      }

      if (!user) {
        return {
          stories: [],
          hasStories: false,
          method_used: 'api_no_user_data'
        };
      }

      // Extract stories if available
      const stories = [];
      
      if (user.highlight_reel_count > 0 || user.has_highlight_reels) {
        // User has stories/highlights
        stories.push({
          id: `story_${user.id}`,
          url: user.profile_pic_url || user.profile_pic_url_hd,
          type: 'image',
          timestamp: Date.now() / 1000,
          thumbnail: user.profile_pic_url
        });
      }

      return {
        stories,
        hasStories: stories.length > 0,
        method_used: 'instagram_api',
        user_info: {
          id: user.id,
          username: user.username,
          full_name: user.full_name,
          is_private: user.is_private,
          follower_count: user.edge_followed_by?.count || user.follower_count
        }
      };

    } catch (error) {
      console.error('Failed to parse Instagram response:', error);
      return {
        stories: [],
        hasStories: false,
        method_used: 'api_parse_error'
      };
    }
  }

  private async fallbackWebScraping(account: any, username: string): Promise<any> {
    try {
      // Simple web scraping fallback
      const response = await axios.get(`https://www.instagram.com/${username}/`, {
        headers: {
          'User-Agent': account.session.userAgent,
          'Cookie': this.buildCookieString(account.session)
        },
        timeout: 15000
      });

      // Basic parsing for story indicators
      const hasStories = response.data.includes('highlight_reel_count') || 
                        response.data.includes('has_highlight_reels');

      return {
        stories: hasStories ? [{
          id: 'web_scraped_story',
          url: 'placeholder',
          type: 'image',
          timestamp: Date.now() / 1000,
          thumbnail: 'placeholder'
        }] : [],
        hasStories,
        method_used: 'web_scraping'
      };

    } catch (error) {
      console.error('Web scraping fallback failed:', error);
      return {
        stories: [],
        hasStories: false,
        method_used: 'fallback_failed'
      };
    }
  }

  private formatResponse(
    result: any,
    startTime: number,
    request: SmartStoryRequest,
    distribution?: any
  ): SmartStoryResponse {
    const processingTime = Date.now() - startTime;

    return {
      stories: result.stories || [],
      hasStories: result.hasStories || false,
      method_used: result.method_used || 'smart_distribution',
      account_used: distribution?.account?.credentials?.username,
      processing_time_ms: processingTime,
      distribution_info: {
        account_health: distribution?.account?.health?.score || 0,
        risk_score: distribution?.riskAssessment?.level === 'high' ? 80 : 
                   distribution?.riskAssessment?.level === 'medium' ? 50 : 20,
        confidence: distribution?.confidence || 0,
        reasoning: distribution?.reasoning || []
      },
      rate_limit_info: {
        remaining: 100, // Would be calculated from actual rate limits
        reset_time: Date.now() + (60 * 60 * 1000),
        daily_limit: 100
      },
      system_status: this.getSystemStatus()
    };
  }

  private getSystemStatus() {
    const poolStats = this.accountPool.getPoolStats();
    const systemStatus = this.resilienceManager.getSystemStatus();

    return {
      pool_utilization: poolStats.available / poolStats.total,
      queue_length: systemStatus.queueStatus.length,
      circuit_breaker_state: systemStatus.circuitBreaker.state
    };
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get comprehensive system status
   */
  getSystemStatus() {
    if (!this.isInitialized) {
      return { initialized: false };
    }

    return {
      initialized: true,
      pool: this.accountPool.getPoolStats(),
      health: this.healthMonitor.getHealthStats(),
      sessions: this.sessionManager.getSessionStats(),
      traffic: this.resilienceManager.getTrafficMetrics(),
      distribution: this.distributor.getDistributionStats(),
      configuration: this.configManager.getConfiguration()
    };
  }

  /**
   * Administrative methods
   */
  async refreshUnhealthyAccounts(): Promise<void> {
    await this.configManager.executeCommand({
      action: 'refresh',
      filters: { healthBelow: 50 }
    });
  }

  async importAccounts(filePath: string, format: 'json' | 'csv' | 'txt' = 'json') {
    return await this.configManager.importAccounts(filePath, format);
  }

  async exportAccountStats(format: 'json' | 'csv' = 'json') {
    return await this.configManager.exportAccounts(format, true);
  }

  async generateAnalytics() {
    return await this.configManager.generateAnalytics();
  }

  /**
   * Health check endpoint
   */
  async healthCheck() {
    const poolStats = this.accountPool.getPoolStats();
    const healthStats = this.healthMonitor.getHealthStats();

    return {
      status: poolStats.available > 0 ? 'healthy' : 'degraded',
      accounts: {
        total: poolStats.total,
        available: poolStats.available,
        healthy: healthStats.healthyAccounts
      },
      performance: {
        averageHealth: poolStats.averageHealth,
        successRate: poolStats.successRate
      },
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get account management interface
   */
  getManagementInterface() {
    return {
      pool: this.accountPool,
      distributor: this.distributor,
      rateLimiter: this.rateLimiter,
      healthMonitor: this.healthMonitor,
      sessionManager: this.sessionManager,
      resilienceManager: this.resilienceManager,
      configManager: this.configManager
    };
  }

  /**
   * Cleanup all services
   */
  async cleanup() {
    if (!this.isInitialized) return;

    console.log('🧹 Cleaning up Smart Instagram Service Integration...');

    await Promise.all([
      this.accountPool.cleanup(),
      this.healthMonitor.cleanup(),
      this.sessionManager.cleanup(),
      this.resilienceManager.cleanup(),
      this.rateLimiter.cleanup()
    ]);

    this.isInitialized = false;
    console.log('✅ Cleanup completed');
  }
}

// Singleton instance for global access
let smartInstagramService: SmartInstagramServiceIntegration | null = null;

export function getSmartInstagramService(userRateLimiter?: RateLimitService): SmartInstagramServiceIntegration {
  if (!smartInstagramService && userRateLimiter) {
    smartInstagramService = new SmartInstagramServiceIntegration(userRateLimiter);
  }

  if (!smartInstagramService) {
    throw new Error('Smart Instagram Service not initialized');
  }

  return smartInstagramService;
}
