// Account Migration Utility
// Migrates basic accounts to enhanced format with health tracking

import fs from 'fs/promises';
import path from 'path';
import { EnhancedAccountCredentials } from '../services/intelligent-session-manager';

export interface BasicAccountCredentials {
  username: string;
  password: string;
  twoFactorKey?: string;
  proxy?: string;
  country?: string;
  priority?: number;
  tags?: string[];
  notes?: string;
}

export class AccountMigrationUtility {
  private basicAccountsPath: string;
  private enhancedAccountsPath: string;
  private backupPath: string;

  constructor() {
    this.basicAccountsPath = path.join(process.cwd(), 'data', 'accounts.json');
    this.enhancedAccountsPath = path.join(process.cwd(), 'data', 'enhanced-accounts.json');
    this.backupPath = path.join(process.cwd(), 'data', 'accounts-backup.json');
  }

  async migrateAccounts(): Promise<{
    migrated: number;
    skipped: number;
    errors: string[];
  }> {
    console.log('🔄 Starting account migration to enhanced format...');
    
    const result = {
      migrated: 0,
      skipped: 0,
      errors: [] as string[]
    };

    try {
      // Check if enhanced accounts already exist
      try {
        await fs.access(this.enhancedAccountsPath);
        console.log('⚠️ Enhanced accounts file already exists');
        
        const choice = await this.promptOverwrite();
        if (!choice) {
          console.log('❌ Migration cancelled by user');
          return result;
        }
      } catch {
        // File doesn't exist, proceed with migration
      }

      // Load basic accounts
      const basicAccounts = await this.loadBasicAccounts();
      if (basicAccounts.length === 0) {
        throw new Error('No basic accounts found to migrate');
      }

      console.log(`📊 Found ${basicAccounts.length} basic accounts to migrate`);

      // Create backup
      await this.createBackup(basicAccounts);

      // Migrate each account
      const enhancedAccounts: EnhancedAccountCredentials[] = [];

      for (const basicAccount of basicAccounts) {
        try {
          const enhancedAccount = this.enhanceAccount(basicAccount);
          enhancedAccounts.push(enhancedAccount);
          result.migrated++;
          
          console.log(`✅ Migrated: ${basicAccount.username}`);
        } catch (error) {
          const errorMsg = `Failed to migrate ${basicAccount.username}: ${error}`;
          result.errors.push(errorMsg);
          console.error(`❌ ${errorMsg}`);
          result.skipped++;
        }
      }

      // Save enhanced accounts
      await this.saveEnhancedAccounts(enhancedAccounts);

      console.log(`🎉 Migration completed successfully!`);
      console.log(`   - Migrated: ${result.migrated} accounts`);
      console.log(`   - Skipped: ${result.skipped} accounts`);
      console.log(`   - Errors: ${result.errors.length}`);

      if (result.errors.length > 0) {
        console.log('❌ Migration errors:');
        result.errors.forEach(error => console.log(`   - ${error}`));
      }

      return result;

    } catch (error) {
      console.error('❌ Migration failed:', error);
      result.errors.push(`Migration failed: ${error}`);
      return result;
    }
  }

  private async loadBasicAccounts(): Promise<BasicAccountCredentials[]> {
    try {
      const data = await fs.readFile(this.basicAccountsPath, 'utf-8');
      const accounts = JSON.parse(data);
      
      if (!Array.isArray(accounts)) {
        throw new Error('Invalid accounts format - expected array');
      }

      return accounts;
    } catch (error) {
      throw new Error(`Failed to load basic accounts: ${error}`);
    }
  }

  private enhanceAccount(basicAccount: BasicAccountCredentials): EnhancedAccountCredentials {
    // Validate required fields
    if (!basicAccount.username || !basicAccount.password) {
      throw new Error('Missing required fields: username or password');
    }

    // Create enhanced account with health tracking
    const enhancedAccount: EnhancedAccountCredentials = {
      // Copy basic fields
      username: basicAccount.username,
      password: basicAccount.password,
      twoFactorKey: basicAccount.twoFactorKey,
      proxy: basicAccount.proxy,
      country: basicAccount.country || 'US',
      priority: basicAccount.priority || 5,
      tags: basicAccount.tags || ['standard'],
      notes: basicAccount.notes,

      // Add health tracking fields
      status: 'healthy',
      hourly_request_count: 0,
      consecutive_failures: 0,
      total_requests_today: 0,
      
      // Initialize timestamps
      last_login_attempt: undefined,
      last_successful_login: undefined,
      session_start_time: undefined,
      cooldown_until: undefined,
      
      // Initialize error tracking
      last_error: undefined
    };

    return enhancedAccount;
  }

  private async createBackup(accounts: BasicAccountCredentials[]): Promise<void> {
    try {
      const backupData = {
        timestamp: new Date().toISOString(),
        accounts: accounts,
        migration_version: '1.0.0'
      };

      await fs.writeFile(this.backupPath, JSON.stringify(backupData, null, 2));
      console.log(`💾 Backup created: ${this.backupPath}`);
    } catch (error) {
      console.warn(`⚠️ Failed to create backup: ${error}`);
    }
  }

  private async saveEnhancedAccounts(accounts: EnhancedAccountCredentials[]): Promise<void> {
    try {
      await fs.writeFile(this.enhancedAccountsPath, JSON.stringify(accounts, null, 2));
      console.log(`💾 Enhanced accounts saved: ${this.enhancedAccountsPath}`);
    } catch (error) {
      throw new Error(`Failed to save enhanced accounts: ${error}`);
    }
  }

  private async promptOverwrite(): Promise<boolean> {
    // In a real implementation, you might want to use a proper prompt library
    // For now, we'll default to true for automated migration
    console.log('🤔 Enhanced accounts file exists. Proceeding with overwrite...');
    return true;
  }

  async validateMigration(): Promise<{
    isValid: boolean;
    issues: string[];
    summary: {
      totalAccounts: number;
      accountsWithTwoFA: number;
      accountsWithProxy: number;
      averagePriority: number;
    };
  }> {
    console.log('🔍 Validating migrated accounts...');
    
    const result = {
      isValid: true,
      issues: [] as string[],
      summary: {
        totalAccounts: 0,
        accountsWithTwoFA: 0,
        accountsWithProxy: 0,
        averagePriority: 0
      }
    };

    try {
      const data = await fs.readFile(this.enhancedAccountsPath, 'utf-8');
      const accounts: EnhancedAccountCredentials[] = JSON.parse(data);

      result.summary.totalAccounts = accounts.length;

      for (const account of accounts) {
        // Validate required fields
        if (!account.username) {
          result.issues.push(`Account missing username`);
          result.isValid = false;
        }

        if (!account.password) {
          result.issues.push(`Account ${account.username} missing password`);
          result.isValid = false;
        }

        // Validate enhanced fields
        if (account.status === undefined) {
          result.issues.push(`Account ${account.username} missing status field`);
          result.isValid = false;
        }

        if (account.hourly_request_count === undefined) {
          result.issues.push(`Account ${account.username} missing hourly_request_count field`);
          result.isValid = false;
        }

        if (account.consecutive_failures === undefined) {
          result.issues.push(`Account ${account.username} missing consecutive_failures field`);
          result.isValid = false;
        }

        // Count features
        if (account.twoFactorKey) {
          result.summary.accountsWithTwoFA++;
        }

        if (account.proxy) {
          result.summary.accountsWithProxy++;
        }

        result.summary.averagePriority += account.priority || 5;
      }

      result.summary.averagePriority = result.summary.averagePriority / accounts.length;

      console.log(`✅ Validation completed`);
      console.log(`   - Total accounts: ${result.summary.totalAccounts}`);
      console.log(`   - Accounts with 2FA: ${result.summary.accountsWithTwoFA}`);
      console.log(`   - Accounts with proxy: ${result.summary.accountsWithProxy}`);
      console.log(`   - Average priority: ${result.summary.averagePriority.toFixed(1)}`);
      console.log(`   - Issues found: ${result.issues.length}`);

      if (result.issues.length > 0) {
        console.log('❌ Validation issues:');
        result.issues.forEach(issue => console.log(`   - ${issue}`));
      }

    } catch (error) {
      result.isValid = false;
      result.issues.push(`Failed to validate: ${error}`);
    }

    return result;
  }

  async rollbackMigration(): Promise<boolean> {
    console.log('🔄 Rolling back migration...');

    try {
      // Check if backup exists
      await fs.access(this.backupPath);

      // Load backup
      const backupData = await fs.readFile(this.backupPath, 'utf-8');
      const backup = JSON.parse(backupData);

      // Restore original accounts
      await fs.writeFile(this.basicAccountsPath, JSON.stringify(backup.accounts, null, 2));

      // Remove enhanced accounts file
      try {
        await fs.unlink(this.enhancedAccountsPath);
      } catch {
        // File might not exist
      }

      console.log('✅ Migration rolled back successfully');
      return true;

    } catch (error) {
      console.error('❌ Rollback failed:', error);
      return false;
    }
  }

  async getAccountStatistics(): Promise<{
    basic: any;
    enhanced: any;
  }> {
    const stats = {
      basic: null,
      enhanced: null
    };

    // Basic accounts stats
    try {
      const basicData = await fs.readFile(this.basicAccountsPath, 'utf-8');
      const basicAccounts = JSON.parse(basicData);
      
      stats.basic = {
        exists: true,
        count: basicAccounts.length,
        withTwoFA: basicAccounts.filter((acc: any) => acc.twoFactorKey).length,
        withProxy: basicAccounts.filter((acc: any) => acc.proxy).length
      };
    } catch {
      stats.basic = { exists: false };
    }

    // Enhanced accounts stats
    try {
      const enhancedData = await fs.readFile(this.enhancedAccountsPath, 'utf-8');
      const enhancedAccounts = JSON.parse(enhancedData);
      
      stats.enhanced = {
        exists: true,
        count: enhancedAccounts.length,
        healthy: enhancedAccounts.filter((acc: any) => acc.status === 'healthy').length,
        withErrors: enhancedAccounts.filter((acc: any) => acc.status === 'error').length,
        withTwoFA: enhancedAccounts.filter((acc: any) => acc.twoFactorKey).length
      };
    } catch {
      stats.enhanced = { exists: false };
    }

    return stats;
  }
}
