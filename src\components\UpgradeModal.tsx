'use client';

import React, { useState } from 'react';
import { X, Crown, Zap, Check, Star, Shield, Clock } from 'lucide-react';
import { UserTier } from '@/lib/types/rate-limiting';

interface UpgradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentTier: UserTier;
  onUpgrade: (tier: UserTier, billingCycle: 'monthly' | 'yearly') => Promise<void>;
}

export function UpgradeModal({ isOpen, onClose, currentTier, onUpgrade }: UpgradeModalProps) {
  const [selectedTier, setSelectedTier] = useState<UserTier>(UserTier.PREMIUM);
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [isProcessing, setIsProcessing] = useState(false);
  const [showComingSoonTooltip, setShowComingSoonTooltip] = useState(false);

  if (!isOpen) return null;

  const tiers = [
    {
      tier: UserTier.PREMIUM,
      name: 'Premium',
      icon: <Crown className="h-6 w-6 text-yellow-500" />,
      monthlyPrice: 9.99,
      yearlyPrice: 99.99,
      color: 'yellow',
      popular: true,
      features: [
        '50 requests per day',
        'HD downloads',
        'Priority support',
        'Advanced features',
        'No advertisements',
        'Faster processing'
      ]
    },
    {
      tier: UserTier.ENTERPRISE,
      name: 'Enterprise',
      icon: <Crown className="h-6 w-6 text-purple-500" />,
      monthlyPrice: 49.99,
      yearlyPrice: 499.99,
      color: 'purple',
      popular: false,
      features: [
        '1,000 requests per day',
        'API access',
        'Custom rate limits',
        'Priority support',
        'Advanced analytics',
        'White-label options',
        'Dedicated support',
        'SLA guarantee'
      ]
    }
  ];

  const handleUpgrade = async () => {
    // Disabled - Coming Soon functionality
    setShowComingSoonTooltip(true);
    setTimeout(() => setShowComingSoonTooltip(false), 3000);
  };

  const getPrice = (tier: any) => {
    const price = billingCycle === 'monthly' ? tier.monthlyPrice : tier.yearlyPrice;
    const period = billingCycle === 'monthly' ? 'month' : 'year';
    const savings = billingCycle === 'yearly' ? Math.round(((tier.monthlyPrice * 12 - tier.yearlyPrice) / (tier.monthlyPrice * 12)) * 100) : 0;
    
    return { price, period, savings };
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <Zap className="h-6 w-6 text-blue-500" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Upgrade Your Plan
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Current Tier */}
          <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-blue-500" />
              <span className="text-sm text-blue-800 dark:text-blue-200">
                Current plan: <strong>{currentTier.toUpperCase()}</strong>
              </span>
            </div>
          </div>

          {/* Billing Cycle Toggle */}
          <div className="mb-6 flex justify-center">
            <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-1 flex">
              <button
                onClick={() => setBillingCycle('monthly')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  billingCycle === 'monthly'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400'
                }`}
              >
                Monthly
              </button>
              <button
                onClick={() => setBillingCycle('yearly')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors relative ${
                  billingCycle === 'yearly'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400'
                }`}
              >
                Yearly
                <span className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-1 rounded">
                  Save 17%
                </span>
              </button>
            </div>
          </div>

          {/* Tier Cards */}
          <div className="grid md:grid-cols-2 gap-6 mb-6">
            {tiers.map((tier) => {
              const { price, period, savings } = getPrice(tier);
              const isSelected = selectedTier === tier.tier;
              const colorClasses = {
                yellow: {
                  border: 'border-yellow-200 dark:border-yellow-800',
                  bg: 'bg-yellow-50 dark:bg-yellow-900/20',
                  button: 'bg-yellow-600 hover:bg-yellow-700',
                  text: 'text-yellow-800 dark:text-yellow-200'
                },
                purple: {
                  border: 'border-purple-200 dark:border-purple-800',
                  bg: 'bg-purple-50 dark:bg-purple-900/20',
                  button: 'bg-purple-600 hover:bg-purple-700',
                  text: 'text-purple-800 dark:text-purple-200'
                }
              };

              return (
                <div
                  key={tier.tier}
                  className={`relative border-2 rounded-lg p-6 cursor-not-allowed opacity-75 transition-all ${
                    isSelected
                      ? `${colorClasses[tier.color as keyof typeof colorClasses].border} ${colorClasses[tier.color as keyof typeof colorClasses].bg}`
                      : 'border-gray-200 dark:border-gray-700'
                  }`}
                  onClick={() => {
                    setShowComingSoonTooltip(true);
                    setTimeout(() => setShowComingSoonTooltip(false), 3000);
                  }}
                  title="Coming Soon"
                >
                  {tier.popular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1">
                        <Star className="h-3 w-3" />
                        <span>Most Popular</span>
                      </span>
                    </div>
                  )}

                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      {tier.icon}
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {tier.name}
                      </h3>
                    </div>
                    {isSelected && (
                      <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                        <Check className="h-4 w-4 text-white" />
                      </div>
                    )}
                  </div>

                  <div className="mb-4">
                    <div className="flex items-baseline space-x-1">
                      <span className="text-3xl font-bold text-gray-900 dark:text-white">
                        ${price}
                      </span>
                      <span className="text-gray-600 dark:text-gray-400">
                        /{period}
                      </span>
                    </div>
                    {savings > 0 && (
                      <p className="text-sm text-green-600 dark:text-green-400">
                        Save {savings}% with yearly billing
                      </p>
                    )}
                  </div>

                  <ul className="space-y-2 mb-6">
                    {tier.features.map((feature, index) => (
                      <li key={index} className="flex items-center space-x-2">
                        <Check className="h-4 w-4 text-green-500 flex-shrink-0" />
                        <span className="text-sm text-gray-700 dark:text-gray-300">
                          {feature}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              );
            })}
          </div>

          {/* Security Notice */}
          <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="flex items-start space-x-2">
              <Shield className="h-5 w-5 text-gray-500 mt-0.5" />
              <div className="text-sm text-gray-600 dark:text-gray-400">
                <p className="font-medium mb-1">Secure Payment</p>
                <p>Your payment information is encrypted and secure. You can cancel anytime.</p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center p-6 border-t border-gray-200 dark:border-gray-700">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-center space-x-1">
              <Clock className="h-4 w-4" />
              <span>Instant activation • Cancel anytime</span>
            </div>
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
            >
              Cancel
            </button>
            <div className="relative">
              <button
                onClick={handleUpgrade}
                disabled={true}
                className="px-6 py-2 bg-gray-400 text-gray-600 rounded-md cursor-not-allowed opacity-75 flex items-center space-x-2"
                title="Coming Soon"
              >
                <Zap className="h-4 w-4" />
                <span>Coming Soon</span>
              </button>

              {/* Coming Soon Tooltip */}
              {showComingSoonTooltip && (
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg shadow-lg z-10">
                  <div className="text-center">
                    <div className="font-medium">Coming Soon!</div>
                    <div className="text-xs opacity-90">Upgrade functionality will be available soon</div>
                  </div>
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default UpgradeModal;
