import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: {
    default: 'Instagram Story Viewer - View & Download Stories Anonymously',
    template: '%s | Instagram Story Viewer'
  },
  description: 'View and download Instagram stories anonymously with our advanced story viewer. Fast, secure, and reliable with smart account management technology. No registration required.',
  keywords: [
    'Instagram story viewer',
    'Instagram story downloader',
    'anonymous Instagram viewer',
    'view Instagram stories',
    'download Instagram stories',
    'Instagram story viewer online',
    'free Instagram story viewer',
    'Instagram story viewer without login',
    'Mollygram alternative',
    'best Instagram story viewer'
  ].join(', '),
  authors: [{ name: 'Instagram Story Viewer Team' }],
  creator: 'Instagram Story Viewer',
  publisher: 'Instagram Story Viewer',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://your-domain.com',
    siteName: 'Instagram Story Viewer',
    title: 'Instagram Story Viewer - View & Download Stories Anonymously',
    description: 'View and download Instagram stories anonymously with our advanced story viewer. Fast, secure, and reliable with smart account management technology.',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Instagram Story Viewer - Anonymous Story Viewing',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Instagram Story Viewer - View & Download Stories Anonymously',
    description: 'View and download Instagram stories anonymously with our advanced story viewer. Fast, secure, and reliable.',
    images: ['/og-image.jpg'],
    creator: '@instastoryviewer',
  },
  category: 'technology',
  classification: 'Social Media Tools',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Structured Data for SEO */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebApplication",
              "name": "Instagram Story Viewer",
              "description": "View and download Instagram stories anonymously with advanced smart account management technology",
              "url": "https://your-domain.com",
              "applicationCategory": "SocialNetworkingApplication",
              "operatingSystem": "Any",
              "permissions": "No registration required",
              "offers": {
                "@type": "Offer",
                "price": "0",
                "priceCurrency": "USD",
                "availability": "https://schema.org/InStock"
              },
              "featureList": [
                "Anonymous Instagram story viewing",
                "HD story downloads",
                "Video playback support",
                "Smart account management",
                "High-traffic resilience",
                "No registration required",
                "Mobile optimized",
                "Fast loading times"
              ],
              "aggregateRating": {
                "@type": "AggregateRating",
                "ratingValue": "4.8",
                "ratingCount": "1250",
                "bestRating": "5",
                "worstRating": "1"
              },
              "author": {
                "@type": "Organization",
                "name": "Instagram Story Viewer Team"
              },
              "provider": {
                "@type": "Organization",
                "name": "Instagram Story Viewer",
                "url": "https://your-domain.com"
              }
            })
          }}
        />

        {/* Additional SEO Meta Tags */}
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
        <meta name="theme-color" content="#6366f1" />
        <meta name="format-detection" content="telephone=no" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        suppressHydrationWarning
      >
        {children}
      </body>
    </html>
  );
}
