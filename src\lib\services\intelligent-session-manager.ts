// Intelligent Instagram Session Management System
// Maintains exactly 3 active sessions with smart rotation and health tracking

import fs from 'fs/promises';
import path from 'path';
import { InstagramSessionGenerator } from './instagram-session-generator';
import { config } from '@/lib/config/env';

export interface AccountStatus {
  healthy: 'healthy';
  unhealthy: 'unhealthy';
  rate_limited: 'rate_limited';
  error: 'error';
}

export interface EnhancedAccountCredentials {
  username: string;
  password: string;
  twoFactorKey?: string;
  proxy?: string;
  country?: string;
  priority?: number;
  tags?: string[];
  notes?: string;
  
  // Enhanced tracking fields
  status: keyof AccountStatus;
  last_login_attempt?: number;
  last_successful_login?: number;
  hourly_request_count: number;
  last_error?: {
    message: string;
    timestamp: number;
    type: 'login' | 'session' | 'request' | 'network';
  };
  consecutive_failures: number;
  total_requests_today: number;
  session_start_time?: number;
  cooldown_until?: number;
}

export interface ActiveSession {
  accountId: string;
  username: string;
  sessionData: any;
  createdAt: number;
  lastUsed: number;
  requestCount: number;
  isHealthy: boolean;
  keepaliveInterval?: NodeJS.Timeout;
}

export interface SessionPoolMetrics {
  activeSessions: number;
  healthyAccounts: number;
  totalAccounts: number;
  averageSessionAge: number;
  requestsPerHour: number;
  rotationsToday: number;
  errorRate: number;
}

export class IntelligentSessionManager {
  private accounts: Map<string, EnhancedAccountCredentials> = new Map();
  private activeSessions: Map<string, ActiveSession> = new Map();
  private sessionGenerator: InstagramSessionGenerator;
  private dataPath: string;
  private metricsPath: string;
  private isInitialized = false;
  
  // Configuration
  private readonly MAX_ACTIVE_SESSIONS = 3;
  private readonly MAX_REQUESTS_PER_HOUR = 5;
  private readonly SESSION_KEEPALIVE_INTERVAL = 5 * 60 * 1000; // 5 minutes
  private readonly ACCOUNT_COOLDOWN_PERIOD = 2 * 60 * 60 * 1000; // 2 hours
  private readonly MAX_CONSECUTIVE_FAILURES = 3;
  private readonly SESSION_WARMUP_THRESHOLD = 0.8; // Start warming next session at 80% usage
  
  // Metrics tracking
  private metrics = {
    rotationsToday: 0,
    totalRequests: 0,
    errorCount: 0,
    lastResetTime: Date.now()
  };

  constructor() {
    this.dataPath = path.join(process.cwd(), 'data', 'enhanced-accounts.json');
    this.metricsPath = path.join(process.cwd(), 'data', 'session-metrics.json');
    this.sessionGenerator = new InstagramSessionGenerator();
  }

  async initialize(): Promise<void> {
    console.log('🚀 Initializing Intelligent Session Manager...');
    
    await this.loadAccounts();
    await this.loadMetrics();
    await this.initializeSessionPool();
    
    // Start background processes
    this.startHealthMonitoring();
    this.startSessionKeepalive();
    this.startPredictiveWarming();
    
    this.isInitialized = true;
    console.log('✅ Intelligent Session Manager initialized successfully');
  }

  private async loadAccounts(): Promise<void> {
    try {
      // First try to load enhanced accounts
      const data = await fs.readFile(this.dataPath, 'utf-8');
      const accountsData = JSON.parse(data);
      
      for (const account of accountsData) {
        this.accounts.set(account.username, account);
      }
      
      console.log(`📊 Loaded ${this.accounts.size} enhanced accounts`);
    } catch (error) {
      // Fallback to basic accounts.json and enhance them
      await this.migrateFromBasicAccounts();
    }
  }

  private async migrateFromBasicAccounts(): Promise<void> {
    console.log('🔄 Migrating from basic accounts to enhanced format...');
    
    const basicAccountsPath = path.join(process.cwd(), 'data', 'accounts.json');
    try {
      const data = await fs.readFile(basicAccountsPath, 'utf-8');
      const basicAccounts = JSON.parse(data);
      
      for (const account of basicAccounts) {
        const enhancedAccount: EnhancedAccountCredentials = {
          ...account,
          status: 'healthy',
          hourly_request_count: 0,
          consecutive_failures: 0,
          total_requests_today: 0
        };
        
        this.accounts.set(account.username, enhancedAccount);
      }
      
      await this.saveAccounts();
      console.log(`✅ Migrated ${this.accounts.size} accounts to enhanced format`);
    } catch (error) {
      console.error('❌ Failed to migrate accounts:', error);
      throw new Error('No account data found');
    }
  }

  private async initializeSessionPool(): Promise<void> {
    console.log('🔧 Initializing session pool with 3 active sessions...');
    
    const healthyAccounts = this.getHealthyAccounts();
    if (healthyAccounts.length < this.MAX_ACTIVE_SESSIONS) {
      console.warn(`⚠️ Only ${healthyAccounts.length} healthy accounts available`);
    }
    
    // Start with the best 3 accounts
    const selectedAccounts = this.selectBestAccounts(this.MAX_ACTIVE_SESSIONS);
    
    for (const account of selectedAccounts) {
      try {
        await this.createSession(account);
        await this.delay(2000); // 2 second delay between logins
      } catch (error) {
        console.error(`❌ Failed to create initial session for ${account.username}:`, error);
        this.markAccountError(account, error as Error, 'login');
      }
    }
    
    console.log(`✅ Initialized ${this.activeSessions.size} active sessions`);
  }

  private async createSession(account: EnhancedAccountCredentials): Promise<ActiveSession | null> {
    console.log(`🔐 Creating session for ${account.username}...`);
    
    try {
      account.last_login_attempt = Date.now();
      
      const sessionData = await this.sessionGenerator.generateSingleSession({
        username: account.username,
        password: account.password,
        twoFactorKey: account.twoFactorKey,
        proxy: account.proxy,
        country: account.country
      });
      
      if (!sessionData) {
        throw new Error('Session generation failed');
      }
      
      const session: ActiveSession = {
        accountId: account.username,
        username: account.username,
        sessionData,
        createdAt: Date.now(),
        lastUsed: Date.now(),
        requestCount: 0,
        isHealthy: true
      };
      
      // Start keepalive for this session
      session.keepaliveInterval = setInterval(() => {
        this.performKeepalive(session);
      }, this.SESSION_KEEPALIVE_INTERVAL);
      
      this.activeSessions.set(account.username, session);
      
      // Update account status
      account.status = 'healthy';
      account.last_successful_login = Date.now();
      account.session_start_time = Date.now();
      account.consecutive_failures = 0;
      
      await this.saveAccounts();
      
      console.log(`✅ Session created successfully for ${account.username}`);
      return session;
      
    } catch (error) {
      console.error(`❌ Session creation failed for ${account.username}:`, error);
      this.markAccountError(account, error as Error, 'login');
      return null;
    }
  }

  async getAvailableSession(): Promise<ActiveSession | null> {
    if (!this.isInitialized) {
      throw new Error('Session manager not initialized');
    }
    
    // Find the best available session
    const availableSession = this.selectBestSession();
    
    if (!availableSession) {
      console.log('🔄 No available sessions, attempting rotation...');
      await this.rotateSession();
      return this.selectBestSession();
    }
    
    // Check if we need to start warming up next session
    if (this.shouldWarmupNextSession(availableSession)) {
      this.warmupNextSession();
    }
    
    // Update usage tracking
    availableSession.lastUsed = Date.now();
    availableSession.requestCount++;
    
    const account = this.accounts.get(availableSession.username);
    if (account) {
      account.hourly_request_count++;
      account.total_requests_today++;
      await this.saveAccounts();
    }
    
    this.metrics.totalRequests++;
    
    console.log(`🎯 Using session: ${availableSession.username} (${availableSession.requestCount}/${this.MAX_REQUESTS_PER_HOUR} requests)`);
    
    return availableSession;
  }

  private selectBestSession(): ActiveSession | null {
    const healthySessions = Array.from(this.activeSessions.values())
      .filter(session => 
        session.isHealthy && 
        session.requestCount < this.MAX_REQUESTS_PER_HOUR
      );
    
    if (healthySessions.length === 0) {
      return null;
    }
    
    // Sort by usage (prefer least used)
    healthySessions.sort((a, b) => {
      // Primary: request count (lower is better)
      if (a.requestCount !== b.requestCount) {
        return a.requestCount - b.requestCount;
      }
      
      // Secondary: last used time (older is better)
      return a.lastUsed - b.lastUsed;
    });
    
    return healthySessions[0];
  }

  private shouldWarmupNextSession(currentSession: ActiveSession): boolean {
    const usageRatio = currentSession.requestCount / this.MAX_REQUESTS_PER_HOUR;
    return usageRatio >= this.SESSION_WARMUP_THRESHOLD;
  }

  private async warmupNextSession(): Promise<void> {
    console.log('🔥 Starting predictive session warmup...');
    
    const availableAccounts = this.getHealthyAccounts()
      .filter(account => !this.activeSessions.has(account.username))
      .filter(account => !this.isInCooldown(account));
    
    if (availableAccounts.length === 0) {
      console.log('⚠️ No accounts available for warmup');
      return;
    }
    
    const nextAccount = this.selectBestAccounts(1, availableAccounts)[0];
    if (nextAccount) {
      // Create session in background (don't await)
      this.createSession(nextAccount).catch(error => {
        console.error('❌ Warmup session creation failed:', error);
      });
    }
  }

  private async rotateSession(): Promise<void> {
    console.log('🔄 Rotating session...');
    
    // Find session that needs rotation
    const sessionToRotate = Array.from(this.activeSessions.values())
      .find(session => 
        session.requestCount >= this.MAX_REQUESTS_PER_HOUR || 
        !session.isHealthy
      );
    
    if (sessionToRotate) {
      await this.retireSession(sessionToRotate);
    }
    
    // Find next best account
    const availableAccounts = this.getHealthyAccounts()
      .filter(account => !this.activeSessions.has(account.username))
      .filter(account => !this.isInCooldown(account));
    
    if (availableAccounts.length > 0) {
      const nextAccount = this.selectBestAccounts(1, availableAccounts)[0];
      await this.createSession(nextAccount);
      this.metrics.rotationsToday++;
    } else {
      console.warn('⚠️ No accounts available for rotation');
    }
  }

  private async retireSession(session: ActiveSession): Promise<void> {
    console.log(`🛑 Retiring session: ${session.username}`);
    
    // Clear keepalive
    if (session.keepaliveInterval) {
      clearInterval(session.keepaliveInterval);
    }
    
    // Set account cooldown
    const account = this.accounts.get(session.username);
    if (account) {
      account.cooldown_until = Date.now() + this.ACCOUNT_COOLDOWN_PERIOD;
      account.hourly_request_count = 0; // Reset for next use
    }
    
    this.activeSessions.delete(session.username);
    await this.saveAccounts();
  }

  private getHealthyAccounts(): EnhancedAccountCredentials[] {
    return Array.from(this.accounts.values())
      .filter(account => 
        account.status === 'healthy' && 
        account.consecutive_failures < this.MAX_CONSECUTIVE_FAILURES
      );
  }

  private selectBestAccounts(count: number, fromAccounts?: EnhancedAccountCredentials[]): EnhancedAccountCredentials[] {
    const candidates = fromAccounts || this.getHealthyAccounts();
    
    // Score accounts based on multiple factors
    const scoredAccounts = candidates.map(account => ({
      account,
      score: this.calculateAccountScore(account)
    }));
    
    // Sort by score (higher is better)
    scoredAccounts.sort((a, b) => b.score - a.score);
    
    return scoredAccounts.slice(0, count).map(item => item.account);
  }

  private calculateAccountScore(account: EnhancedAccountCredentials): number {
    let score = 100; // Base score
    
    // Penalize recent usage
    const hoursSinceLastLogin = account.last_successful_login 
      ? (Date.now() - account.last_successful_login) / (1000 * 60 * 60)
      : 24;
    score += Math.min(hoursSinceLastLogin * 2, 20); // Up to +20 for accounts unused for 10+ hours
    
    // Penalize consecutive failures
    score -= account.consecutive_failures * 15;
    
    // Penalize today's usage
    score -= account.total_requests_today * 2;
    
    // Bonus for priority
    score += (account.priority || 5) * 3;
    
    // Penalize if in cooldown
    if (this.isInCooldown(account)) {
      score -= 50;
    }
    
    return Math.max(score, 0);
  }

  private isInCooldown(account: EnhancedAccountCredentials): boolean {
    return account.cooldown_until ? Date.now() < account.cooldown_until : false;
  }

  private markAccountError(account: EnhancedAccountCredentials, error: Error, type: 'login' | 'session' | 'request' | 'network'): void {
    account.status = 'error';
    account.last_error = {
      message: error.message,
      timestamp: Date.now(),
      type
    };
    account.consecutive_failures++;
    
    this.metrics.errorCount++;
    
    // Auto-retire account if too many failures
    if (account.consecutive_failures >= this.MAX_CONSECUTIVE_FAILURES) {
      console.log(`🚫 Auto-retiring account ${account.username} due to consecutive failures`);
      account.status = 'unhealthy';
      account.cooldown_until = Date.now() + (24 * 60 * 60 * 1000); // 24 hour cooldown
    }
  }

  private async performKeepalive(session: ActiveSession): Promise<void> {
    try {
      // Implement session validation logic here
      // This could be a lightweight Instagram API call
      console.log(`💓 Keepalive for session: ${session.username}`);
      
      // Update session health based on validation
      session.isHealthy = true; // Update based on actual validation
      
    } catch (error) {
      console.error(`❌ Keepalive failed for ${session.username}:`, error);
      session.isHealthy = false;
      
      const account = this.accounts.get(session.username);
      if (account) {
        this.markAccountError(account, error as Error, 'session');
      }
    }
  }

  private startHealthMonitoring(): void {
    setInterval(() => {
      this.performHealthCheck();
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  private startSessionKeepalive(): void {
    // Keepalive is handled per-session in createSession
    console.log('🔄 Session keepalive system started');
  }

  private startPredictiveWarming(): void {
    setInterval(() => {
      this.checkPredictiveWarming();
    }, 2 * 60 * 1000); // Every 2 minutes
  }

  private async performHealthCheck(): Promise<void> {
    console.log('🏥 Performing health check...');
    
    // Check active sessions
    for (const [username, session] of this.activeSessions) {
      if (!session.isHealthy) {
        console.log(`🔄 Rotating unhealthy session: ${username}`);
        await this.retireSession(session);
      }
    }
    
    // Reset hourly counters if needed
    this.resetHourlyCounters();
    
    // Save current state
    await this.saveAccounts();
    await this.saveMetrics();
  }

  private resetHourlyCounters(): void {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    
    for (const account of this.accounts.values()) {
      if (account.session_start_time && (now - account.session_start_time) > oneHour) {
        account.hourly_request_count = 0;
        account.session_start_time = now;
      }
    }
  }

  private checkPredictiveWarming(): void {
    // Check if any active sessions need warmup
    for (const session of this.activeSessions.values()) {
      if (this.shouldWarmupNextSession(session)) {
        this.warmupNextSession();
        break; // Only warm one at a time
      }
    }
  }

  private async saveAccounts(): Promise<void> {
    try {
      const accountsArray = Array.from(this.accounts.values());
      await fs.writeFile(this.dataPath, JSON.stringify(accountsArray, null, 2));
    } catch (error) {
      console.error('❌ Failed to save accounts:', error);
    }
  }

  private async loadMetrics(): Promise<void> {
    try {
      const data = await fs.readFile(this.metricsPath, 'utf-8');
      this.metrics = { ...this.metrics, ...JSON.parse(data) };
    } catch (error) {
      // Metrics file doesn't exist, use defaults
    }
  }

  private async saveMetrics(): Promise<void> {
    try {
      await fs.writeFile(this.metricsPath, JSON.stringify(this.metrics, null, 2));
    } catch (error) {
      console.error('❌ Failed to save metrics:', error);
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Public API methods
  getSessionPoolMetrics(): SessionPoolMetrics {
    const healthyAccounts = this.getHealthyAccounts().length;
    const totalAccounts = this.accounts.size;
    const activeSessions = this.activeSessions.size;
    
    const sessionAges = Array.from(this.activeSessions.values())
      .map(session => Date.now() - session.createdAt);
    const averageSessionAge = sessionAges.length > 0 
      ? sessionAges.reduce((a, b) => a + b, 0) / sessionAges.length 
      : 0;
    
    const requestsPerHour = this.metrics.totalRequests; // Simplified
    const errorRate = this.metrics.errorCount / Math.max(this.metrics.totalRequests, 1);
    
    return {
      activeSessions,
      healthyAccounts,
      totalAccounts,
      averageSessionAge,
      requestsPerHour,
      rotationsToday: this.metrics.rotationsToday,
      errorRate
    };
  }

  async getDetailedStatus(): Promise<any> {
    return {
      initialized: this.isInitialized,
      activeSessions: Array.from(this.activeSessions.entries()).map(([username, session]) => ({
        username,
        requestCount: session.requestCount,
        isHealthy: session.isHealthy,
        ageMinutes: Math.floor((Date.now() - session.createdAt) / (1000 * 60)),
        lastUsedMinutes: Math.floor((Date.now() - session.lastUsed) / (1000 * 60))
      })),
      accountSummary: {
        total: this.accounts.size,
        healthy: this.getHealthyAccounts().length,
        inCooldown: Array.from(this.accounts.values()).filter(acc => this.isInCooldown(acc)).length,
        withErrors: Array.from(this.accounts.values()).filter(acc => acc.status === 'error').length
      },
      metrics: this.getSessionPoolMetrics()
    };
  }

  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up Intelligent Session Manager...');
    
    // Clear all keepalive intervals
    for (const session of this.activeSessions.values()) {
      if (session.keepaliveInterval) {
        clearInterval(session.keepaliveInterval);
      }
    }
    
    // Save final state
    await this.saveAccounts();
    await this.saveMetrics();
    
    // Cleanup session generator
    await this.sessionGenerator.cleanup();
    
    console.log('✅ Cleanup completed');
  }
}
