#!/usr/bin/env node

// Comprehensive test suite for the Intelligent Session Management System
const fs = require('fs');
const path = require('path');

async function testIntelligentSessionManager() {
  console.log('🧪 Testing Intelligent Session Management System...\n');

  const results = {
    passed: 0,
    failed: 0,
    warnings: 0,
    tests: []
  };

  // Test 1: Check if enhanced accounts exist
  console.log('📋 Test 1: Enhanced Accounts File');
  try {
    const enhancedAccountsPath = path.join(process.cwd(), 'data', 'enhanced-accounts.json');
    
    if (fs.existsSync(enhancedAccountsPath)) {
      const data = fs.readFileSync(enhancedAccountsPath, 'utf-8');
      const accounts = JSON.parse(data);
      
      if (Array.isArray(accounts) && accounts.length > 0) {
        console.log(`✅ Enhanced accounts file exists with ${accounts.length} accounts`);
        
        // Validate account structure
        const firstAccount = accounts[0];
        const requiredFields = ['username', 'password', 'status', 'hourly_request_count', 'consecutive_failures'];
        const missingFields = requiredFields.filter(field => !(field in firstAccount));
        
        if (missingFields.length === 0) {
          console.log('✅ Account structure is valid');
          results.passed++;
        } else {
          console.log(`❌ Missing required fields: ${missingFields.join(', ')}`);
          results.failed++;
        }
        
        // Check 2FA coverage
        const accountsWithTwoFA = accounts.filter(acc => acc.twoFactorKey).length;
        const twoFACoverage = (accountsWithTwoFA / accounts.length) * 100;
        
        if (twoFACoverage >= 90) {
          console.log(`✅ Excellent 2FA coverage: ${twoFACoverage.toFixed(1)}%`);
        } else if (twoFACoverage >= 50) {
          console.log(`⚠️ Moderate 2FA coverage: ${twoFACoverage.toFixed(1)}%`);
          results.warnings++;
        } else {
          console.log(`❌ Low 2FA coverage: ${twoFACoverage.toFixed(1)}%`);
          results.failed++;
        }
        
      } else {
        console.log('❌ Enhanced accounts file is empty or invalid');
        results.failed++;
      }
    } else {
      console.log('❌ Enhanced accounts file not found');
      console.log('💡 Run: node scripts/migrate-to-enhanced-accounts.js migrate');
      results.failed++;
    }
  } catch (error) {
    console.log(`❌ Error reading enhanced accounts: ${error.message}`);
    results.failed++;
  }

  console.log('');

  // Test 2: Check TypeScript compilation
  console.log('📋 Test 2: TypeScript Compilation');
  try {
    const { execSync } = require('child_process');
    
    // Check if TypeScript files compile
    execSync('npx tsc --noEmit --project tsconfig.json', { stdio: 'pipe' });
    console.log('✅ TypeScript compilation successful');
    results.passed++;
  } catch (error) {
    console.log('❌ TypeScript compilation failed');
    console.log('💡 Check for type errors in the codebase');
    results.failed++;
  }

  console.log('');

  // Test 3: Check dependencies
  console.log('📋 Test 3: Required Dependencies');
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf-8'));
    const requiredDeps = ['otplib', 'puppeteer', 'axios', 'next'];
    
    let missingDeps = [];
    for (const dep of requiredDeps) {
      if (!packageJson.dependencies[dep] && !packageJson.devDependencies[dep]) {
        missingDeps.push(dep);
      }
    }
    
    if (missingDeps.length === 0) {
      console.log('✅ All required dependencies are present');
      results.passed++;
    } else {
      console.log(`❌ Missing dependencies: ${missingDeps.join(', ')}`);
      console.log('💡 Run: npm install');
      results.failed++;
    }
    
    // Check if otplib is specifically available (critical for 2FA)
    if (packageJson.dependencies['otplib']) {
      console.log('✅ otplib dependency found (2FA support enabled)');
    } else {
      console.log('❌ otplib dependency missing (2FA will not work)');
      results.failed++;
    }
    
  } catch (error) {
    console.log(`❌ Error checking dependencies: ${error.message}`);
    results.failed++;
  }

  console.log('');

  // Test 4: API Route Structure
  console.log('📋 Test 4: API Route Structure');
  try {
    const apiRoutes = [
      'src/app/api/session-pool/route.ts',
      'src/app/api/session-pool/status/route.ts',
      'src/app/api/smart-stories/route.ts'
    ];
    
    let existingRoutes = 0;
    for (const route of apiRoutes) {
      if (fs.existsSync(route)) {
        existingRoutes++;
        console.log(`✅ ${route} exists`);
      } else {
        console.log(`❌ ${route} missing`);
      }
    }
    
    if (existingRoutes === apiRoutes.length) {
      console.log('✅ All API routes are present');
      results.passed++;
    } else {
      console.log(`❌ ${apiRoutes.length - existingRoutes} API routes missing`);
      results.failed++;
    }
    
  } catch (error) {
    console.log(`❌ Error checking API routes: ${error.message}`);
    results.failed++;
  }

  console.log('');

  // Test 5: Component Structure
  console.log('📋 Test 5: Component Structure');
  try {
    const components = [
      'src/components/SessionPoolDashboard.tsx',
      'src/components/ErrorBoundary.tsx',
      'src/components/StoryViewer.tsx'
    ];
    
    let existingComponents = 0;
    for (const component of components) {
      if (fs.existsSync(component)) {
        existingComponents++;
        console.log(`✅ ${component} exists`);
      } else {
        console.log(`❌ ${component} missing`);
      }
    }
    
    if (existingComponents === components.length) {
      console.log('✅ All required components are present');
      results.passed++;
    } else {
      console.log(`❌ ${components.length - existingComponents} components missing`);
      results.failed++;
    }
    
  } catch (error) {
    console.log(`❌ Error checking components: ${error.message}`);
    results.failed++;
  }

  console.log('');

  // Test 6: Service Layer
  console.log('📋 Test 6: Service Layer');
  try {
    const services = [
      'src/lib/services/intelligent-session-manager.ts',
      'src/lib/utils/account-migration.ts',
      'src/lib/config/env.ts'
    ];
    
    let existingServices = 0;
    for (const service of services) {
      if (fs.existsSync(service)) {
        existingServices++;
        console.log(`✅ ${service} exists`);
      } else {
        console.log(`❌ ${service} missing`);
      }
    }
    
    if (existingServices === services.length) {
      console.log('✅ All service layer files are present');
      results.passed++;
    } else {
      console.log(`❌ ${services.length - existingServices} service files missing`);
      results.failed++;
    }
    
  } catch (error) {
    console.log(`❌ Error checking service layer: ${error.message}`);
    results.failed++;
  }

  console.log('');

  // Test 7: Configuration Validation
  console.log('📋 Test 7: Configuration Validation');
  try {
    // Test environment configuration
    const envConfigPath = 'src/lib/config/env.ts';
    if (fs.existsSync(envConfigPath)) {
      console.log('✅ Environment configuration exists');
      
      // Basic validation of config structure
      const configContent = fs.readFileSync(envConfigPath, 'utf-8');
      const requiredConfigKeys = ['adminApiKey', 'nodeEnv', 'headlessMode', 'rateLimiting'];
      
      let foundKeys = 0;
      for (const key of requiredConfigKeys) {
        if (configContent.includes(key)) {
          foundKeys++;
        }
      }
      
      if (foundKeys === requiredConfigKeys.length) {
        console.log('✅ Configuration structure is valid');
        results.passed++;
      } else {
        console.log(`❌ Configuration missing ${requiredConfigKeys.length - foundKeys} required keys`);
        results.failed++;
      }
    } else {
      console.log('❌ Environment configuration missing');
      results.failed++;
    }
    
  } catch (error) {
    console.log(`❌ Error validating configuration: ${error.message}`);
    results.failed++;
  }

  console.log('');

  // Test 8: Data Directory Structure
  console.log('📋 Test 8: Data Directory Structure');
  try {
    const dataDir = path.join(process.cwd(), 'data');
    
    if (fs.existsSync(dataDir)) {
      console.log('✅ Data directory exists');
      
      const expectedFiles = ['accounts.json', 'enhanced-accounts.json'];
      let foundFiles = 0;
      
      for (const file of expectedFiles) {
        const filePath = path.join(dataDir, file);
        if (fs.existsSync(filePath)) {
          foundFiles++;
          console.log(`✅ ${file} exists`);
        } else {
          console.log(`⚠️ ${file} missing (may be normal)`);
        }
      }
      
      if (foundFiles >= 1) {
        console.log('✅ Data directory structure is adequate');
        results.passed++;
      } else {
        console.log('❌ No account files found in data directory');
        results.failed++;
      }
    } else {
      console.log('❌ Data directory missing');
      console.log('💡 Create data directory and add account files');
      results.failed++;
    }
    
  } catch (error) {
    console.log(`❌ Error checking data directory: ${error.message}`);
    results.failed++;
  }

  console.log('');

  // Summary
  console.log('📊 Test Summary:');
  console.log(`   ✅ Passed: ${results.passed}`);
  console.log(`   ❌ Failed: ${results.failed}`);
  console.log(`   ⚠️ Warnings: ${results.warnings}`);
  console.log(`   📊 Total: ${results.passed + results.failed + results.warnings}`);

  const successRate = (results.passed / (results.passed + results.failed)) * 100;
  console.log(`   🎯 Success Rate: ${successRate.toFixed(1)}%`);

  console.log('');

  if (results.failed === 0) {
    console.log('🎉 All tests passed! The Intelligent Session Management System is ready.');
    console.log('');
    console.log('📋 Next steps:');
    console.log('   1. Start the application: npm run dev');
    console.log('   2. Visit the session pool dashboard');
    console.log('   3. Test story viewing functionality');
    console.log('   4. Monitor session health and metrics');
  } else {
    console.log('⚠️ Some tests failed. Please address the issues above before proceeding.');
    console.log('');
    console.log('🔧 Common fixes:');
    console.log('   - Run migration: node scripts/migrate-to-enhanced-accounts.js migrate');
    console.log('   - Install dependencies: npm install');
    console.log('   - Check TypeScript errors: npx tsc --noEmit');
    console.log('   - Verify file structure and permissions');
  }

  return {
    success: results.failed === 0,
    results
  };
}

// Run tests
testIntelligentSessionManager()
  .then(result => {
    process.exit(result.success ? 0 : 1);
  })
  .catch(error => {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  });
