import { NextRequest, NextResponse } from 'next/server';
import { RateLimitTestSuite } from '@/lib/utils/rate-limit-test-utils';

export async function GET(request: NextRequest) {
  // Only allow testing in development environment
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'Rate limit testing is only available in development mode' },
      { status: 403 }
    );
  }

  try {
    const { searchParams } = new URL(request.url);
    const testName = searchParams.get('test');

    const testSuite = new RateLimitTestSuite();

    if (testName) {
      // Run specific test
      let result;
      switch (testName) {
        case 'basic':
          result = await testSuite.testBasicRateLimit();
          break;
        case 'fingerprint':
          result = await testSuite.testFingerprintVariations();
          break;
        case 'ip-changes':
          result = await testSuite.testIPChanges();
          break;
        case 'tier-upgrade':
          result = await testSuite.testTierUpgrade();
          break;
        case 'concurrent':
          result = await testSuite.testConcurrentRequests();
          break;
        case 'reset':
          result = await testSuite.testRateLimitReset();
          break;
        default:
          return NextResponse.json(
            { error: 'Invalid test name' },
            { status: 400 }
          );
      }

      await testSuite.cleanup();
      return NextResponse.json({ test: testName, result });
    } else {
      // Run all tests
      const results = await testSuite.runAllTests();
      return NextResponse.json({
        summary: {
          total: results.passed + results.failed,
          passed: results.passed,
          failed: results.failed,
          success: results.failed === 0
        },
        results: results.results
      });
    }

  } catch (error) {
    console.error('Rate limit test failed:', error);
    return NextResponse.json(
      { error: 'Test execution failed', details: error.message },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  // Only allow testing in development environment
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'Rate limit testing is only available in development mode' },
      { status: 403 }
    );
  }

  try {
    const body = await request.json();
    const { action } = body;

    const testSuite = new RateLimitTestSuite();

    if (action === 'cleanup') {
      await testSuite.cleanup();
      return NextResponse.json({ message: 'Test data cleaned up successfully' });
    }

    if (action === 'generate-test-data') {
      const { count = 5 } = body;
      const fingerprints = [];
      
      for (let i = 0; i < count; i++) {
        const fingerprint = testSuite.generateTestFingerprint({
          userAgent: `TestAgent/${i}`,
          screenResolution: `${1920 + i * 100}x${1080 + i * 50}`
        });
        fingerprints.push(fingerprint);
      }

      return NextResponse.json({
        message: `Generated ${count} test fingerprints`,
        fingerprints: fingerprints.map(fp => ({
          id: fp.fingerprintId,
          userAgent: fp.userAgent,
          screenResolution: fp.screenResolution
        }))
      });
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Rate limit test action failed:', error);
    return NextResponse.json(
      { error: 'Test action failed', details: error.message },
      { status: 500 }
    );
  }
}
