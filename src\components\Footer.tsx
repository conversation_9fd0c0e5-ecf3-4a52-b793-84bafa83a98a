import { Github, Shield, AlertTriangle, Info, Eye, BookOpen, HelpCircle, BarChart3, FileText, Zap, Users, Download } from 'lucide-react';

export function Footer() {
  return (
    <footer className="mt-16 pt-8 border-t border-gray-200 dark:border-gray-700">
      <div className="max-w-6xl mx-auto">
        {/* Main Footer Content */}
        <div className="grid md:grid-cols-4 gap-8 mb-8">
          {/* Brand Section */}
          <div className="md:col-span-1">
            <div className="flex items-center gap-2 mb-4">
              <Eye className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              <span className="text-lg font-bold text-gray-800 dark:text-gray-200">
                Story Viewer
              </span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Advanced Instagram story viewer with smart account management and high-traffic resilience technology.
            </p>
            <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center gap-1">
                <Zap className="w-4 h-4 text-blue-500" />
                <span>Lightning Fast</span>
              </div>
              <div className="flex items-center gap-1">
                <Shield className="w-4 h-4 text-green-500" />
                <span>100% Safe</span>
              </div>
            </div>
          </div>

          {/* Navigation Links */}
          <div>
            <h4 className="font-semibold text-gray-900 dark:text-white mb-4">Navigation</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <a href="/" className="text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 flex items-center gap-2">
                  <Eye className="w-4 h-4" />
                  Story Viewer
                </a>
              </li>
              <li>
                <a href="/tutorial" className="text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 flex items-center gap-2">
                  <BookOpen className="w-4 h-4" />
                  How to Use
                </a>
              </li>
              <li>
                <a href="/comparison" className="text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 flex items-center gap-2">
                  <BarChart3 className="w-4 h-4" />
                  Compare Features
                </a>
              </li>
              <li>
                <a href="/faq" className="text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 flex items-center gap-2">
                  <HelpCircle className="w-4 h-4" />
                  FAQ
                </a>
              </li>
              <li>
                <a href="/blog" className="text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 flex items-center gap-2">
                  <FileText className="w-4 h-4" />
                  Blog & Tips
                </a>
              </li>
            </ul>
          </div>

          {/* Features */}
          <div>
            <h4 className="font-semibold text-gray-900 dark:text-white mb-4">Key Features</h4>
            <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
              <li className="flex items-center gap-2">
                <Shield className="w-4 h-4 text-green-500" />
                Anonymous Story Viewing
              </li>
              <li className="flex items-center gap-2">
                <Download className="w-4 h-4 text-blue-500" />
                HD Story Downloads
              </li>
              <li className="flex items-center gap-2">
                <Zap className="w-4 h-4 text-yellow-500" />
                Lightning Fast Loading
              </li>
              <li className="flex items-center gap-2">
                <Users className="w-4 h-4 text-purple-500" />
                100+ Concurrent Users
              </li>
              <li className="flex items-center gap-2">
                <Eye className="w-4 h-4 text-indigo-500" />
                Video Playback Support
              </li>
            </ul>
          </div>

          {/* Technology */}
          <div>
            <h4 className="font-semibold text-gray-900 dark:text-white mb-4">Technology</h4>
            <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
              <li>Smart Account Management</li>
              <li>Intelligent Request Distribution</li>
              <li>Advanced Rate Limiting</li>
              <li>High-Traffic Resilience</li>
              <li>Circuit Breaker Protection</li>
              <li>Predictive Health Monitoring</li>
            </ul>
          </div>
        </div>
        {/* Disclaimer */}
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-8">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-gray-700 dark:text-gray-300">
              <h4 className="font-semibold mb-2">Important Disclaimer</h4>
              <ul className="space-y-1 list-disc list-inside">
                <li>Only public Instagram content is accessible</li>
                <li>We respect Instagram's Terms of Service and user privacy</li>
                <li>No personal data is stored or tracked</li>
                <li>All content viewing is anonymous and secure</li>
                <li>Use responsibly and ethically</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Features */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <div className="text-center">
            <Shield className="h-8 w-8 text-green-500 mx-auto mb-2" />
            <h4 className="font-semibold text-gray-900 dark:text-white mb-1">
              Privacy Focused
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Anonymous viewing with no data collection
            </p>
          </div>
          
          <div className="text-center">
            <AlertTriangle className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
            <h4 className="font-semibold text-gray-900 dark:text-white mb-1">
              Ethical Usage
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Respects platform rules and user privacy
            </p>
          </div>
          
          <div className="text-center">
            <Github className="h-8 w-8 text-purple-500 mx-auto mb-2" />
            <h4 className="font-semibold text-gray-900 dark:text-white mb-1">
              Open Source
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Transparent and community-driven
            </p>
          </div>
        </div>

        {/* SEO Keywords Section */}
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-8">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-4 text-center">
            Popular Instagram Story Viewer Searches
          </h4>
          <div className="flex flex-wrap justify-center gap-2 text-xs">
            {[
              'Instagram story viewer',
              'Anonymous Instagram viewer',
              'Instagram story downloader',
              'View Instagram stories without account',
              'Mollygram alternative',
              'Best Instagram story viewer',
              'Free Instagram story viewer',
              'Instagram story viewer online',
              'HD Instagram story download',
              'Private Instagram story viewer'
            ].map((keyword, index) => (
              <span key={index} className="px-2 py-1 bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded border">
                {keyword}
              </span>
            ))}
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              <p>© 2024 Instagram Story Viewer - Advanced Story Viewing Technology</p>
              <p className="mt-1">Built with Next.js, TypeScript, and Smart Account Management</p>
            </div>

            <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
              <a href="/privacy" className="hover:text-purple-600 dark:hover:text-purple-400">Privacy Policy</a>
              <a href="/terms" className="hover:text-purple-600 dark:hover:text-purple-400">Terms of Service</a>
              <a href="/sitemap.xml" className="hover:text-purple-600 dark:hover:text-purple-400">Sitemap</a>
            </div>
          </div>

          {/* Trust Badges */}
          <div className="mt-6 flex justify-center items-center gap-6 text-xs text-gray-400 dark:text-gray-500">
            <div className="flex items-center gap-1">
              <Shield className="w-3 h-3 text-green-500" />
              <span>SSL Secured</span>
            </div>
            <div className="flex items-center gap-1">
              <Eye className="w-3 h-3 text-blue-500" />
              <span>Privacy Protected</span>
            </div>
            <div className="flex items-center gap-1">
              <Zap className="w-3 h-3 text-yellow-500" />
              <span>99% Uptime</span>
            </div>
            <div className="flex items-center gap-1">
              <Users className="w-3 h-3 text-purple-500" />
              <span>1000+ Daily Users</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
