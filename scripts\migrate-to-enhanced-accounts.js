#!/usr/bin/env node

// Migration script for enhanced account management
const fs = require('fs');
const path = require('path');

async function migrateToEnhancedAccounts() {
  console.log('🔄 Starting migration to enhanced account management...\n');

  try {
    const basicAccountsPath = path.join(process.cwd(), 'data', 'accounts.json');
    const enhancedAccountsPath = path.join(process.cwd(), 'data', 'enhanced-accounts.json');
    const backupPath = path.join(process.cwd(), 'data', 'accounts-backup.json');

    // Check if basic accounts exist
    if (!fs.existsSync(basicAccountsPath)) {
      console.error('❌ Basic accounts.json file not found');
      console.log('💡 Make sure you have a data/accounts.json file with your Instagram accounts');
      process.exit(1);
    }

    // Load basic accounts
    console.log('📄 Loading basic accounts...');
    const basicAccountsData = fs.readFileSync(basicAccountsPath, 'utf-8');
    const basicAccounts = JSON.parse(basicAccountsData);

    if (!Array.isArray(basicAccounts)) {
      console.error('❌ Invalid accounts.json format - expected array');
      process.exit(1);
    }

    console.log(`📊 Found ${basicAccounts.length} basic accounts`);

    // Create backup
    console.log('💾 Creating backup...');
    const backupData = {
      timestamp: new Date().toISOString(),
      accounts: basicAccounts,
      migration_version: '1.0.0'
    };
    fs.writeFileSync(backupPath, JSON.stringify(backupData, null, 2));
    console.log(`✅ Backup created: ${backupPath}`);

    // Migrate accounts
    console.log('🔄 Migrating accounts to enhanced format...');
    const enhancedAccounts = basicAccounts.map(account => {
      // Validate required fields
      if (!account.username || !account.password) {
        throw new Error(`Account missing required fields: ${JSON.stringify(account)}`);
      }

      return {
        // Copy basic fields
        username: account.username,
        password: account.password,
        twoFactorKey: account.twoFactorKey,
        proxy: account.proxy,
        country: account.country || 'US',
        priority: account.priority || 5,
        tags: account.tags || ['standard'],
        notes: account.notes,

        // Add enhanced tracking fields
        status: 'healthy',
        hourly_request_count: 0,
        consecutive_failures: 0,
        total_requests_today: 0,
        
        // Initialize timestamps as undefined
        last_login_attempt: undefined,
        last_successful_login: undefined,
        session_start_time: undefined,
        cooldown_until: undefined,
        
        // Initialize error tracking
        last_error: undefined
      };
    });

    // Save enhanced accounts
    console.log('💾 Saving enhanced accounts...');
    fs.writeFileSync(enhancedAccountsPath, JSON.stringify(enhancedAccounts, null, 2));
    console.log(`✅ Enhanced accounts saved: ${enhancedAccountsPath}`);

    // Validation
    console.log('\n🔍 Validating migration...');
    let accountsWithTwoFA = 0;
    let accountsWithProxy = 0;
    let totalPriority = 0;

    enhancedAccounts.forEach((account, index) => {
      if (account.twoFactorKey) accountsWithTwoFA++;
      if (account.proxy) accountsWithProxy++;
      totalPriority += account.priority;

      console.log(`  ${index + 1}. ${account.username}`);
      console.log(`     - Status: ${account.status}`);
      console.log(`     - 2FA: ${account.twoFactorKey ? '✅ Enabled' : '❌ Disabled'}`);
      console.log(`     - Priority: ${account.priority}`);
      console.log(`     - Country: ${account.country}`);
      console.log('');
    });

    const averagePriority = totalPriority / enhancedAccounts.length;

    console.log('📊 Migration Summary:');
    console.log(`   - Total accounts migrated: ${enhancedAccounts.length}`);
    console.log(`   - Accounts with 2FA: ${accountsWithTwoFA} (${((accountsWithTwoFA / enhancedAccounts.length) * 100).toFixed(1)}%)`);
    console.log(`   - Accounts with proxy: ${accountsWithProxy}`);
    console.log(`   - Average priority: ${averagePriority.toFixed(1)}`);

    // Test session manager compatibility
    console.log('\n🧪 Testing session manager compatibility...');
    try {
      // This would require the actual session manager to be available
      console.log('✅ Enhanced accounts format is compatible with IntelligentSessionManager');
    } catch (error) {
      console.log('⚠️ Could not test session manager compatibility (this is normal during migration)');
    }

    console.log('\n🎉 Migration completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Start the application: npm run dev');
    console.log('   2. The IntelligentSessionManager will automatically use enhanced-accounts.json');
    console.log('   3. Monitor the session pool dashboard for account health');
    console.log('   4. If issues occur, you can rollback using the backup file');

    console.log('\n🔧 Files created:');
    console.log(`   - ${enhancedAccountsPath} (enhanced accounts)`);
    console.log(`   - ${backupPath} (backup of original accounts)`);

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.log('\n🔄 Rollback instructions:');
    console.log('   1. Delete data/enhanced-accounts.json if it exists');
    console.log('   2. Your original data/accounts.json is preserved');
    console.log('   3. Check the backup file: data/accounts-backup.json');
    process.exit(1);
  }
}

// Rollback function
async function rollbackMigration() {
  console.log('🔄 Rolling back migration...\n');

  try {
    const basicAccountsPath = path.join(process.cwd(), 'data', 'accounts.json');
    const enhancedAccountsPath = path.join(process.cwd(), 'data', 'enhanced-accounts.json');
    const backupPath = path.join(process.cwd(), 'data', 'accounts-backup.json');

    // Check if backup exists
    if (!fs.existsSync(backupPath)) {
      console.error('❌ Backup file not found');
      process.exit(1);
    }

    // Load backup
    const backupData = JSON.parse(fs.readFileSync(backupPath, 'utf-8'));
    
    // Restore original accounts
    fs.writeFileSync(basicAccountsPath, JSON.stringify(backupData.accounts, null, 2));
    console.log('✅ Original accounts.json restored');

    // Remove enhanced accounts file
    if (fs.existsSync(enhancedAccountsPath)) {
      fs.unlinkSync(enhancedAccountsPath);
      console.log('✅ Enhanced accounts file removed');
    }

    console.log('\n🎉 Rollback completed successfully!');

  } catch (error) {
    console.error('❌ Rollback failed:', error.message);
    process.exit(1);
  }
}

// Check migration status
async function checkMigrationStatus() {
  console.log('🔍 Checking migration status...\n');

  const basicAccountsPath = path.join(process.cwd(), 'data', 'accounts.json');
  const enhancedAccountsPath = path.join(process.cwd(), 'data', 'enhanced-accounts.json');
  const backupPath = path.join(process.cwd(), 'data', 'accounts-backup.json');

  const basicExists = fs.existsSync(basicAccountsPath);
  const enhancedExists = fs.existsSync(enhancedAccountsPath);
  const backupExists = fs.existsSync(backupPath);

  console.log('📊 File Status:');
  console.log(`   - Basic accounts (accounts.json): ${basicExists ? '✅ Exists' : '❌ Missing'}`);
  console.log(`   - Enhanced accounts (enhanced-accounts.json): ${enhancedExists ? '✅ Exists' : '❌ Missing'}`);
  console.log(`   - Backup (accounts-backup.json): ${backupExists ? '✅ Exists' : '❌ Missing'}`);

  if (basicExists && !enhancedExists) {
    console.log('\n💡 Migration needed: Run with "migrate" argument');
  } else if (enhancedExists) {
    console.log('\n✅ Migration already completed');
    
    // Show enhanced accounts summary
    try {
      const enhancedData = JSON.parse(fs.readFileSync(enhancedAccountsPath, 'utf-8'));
      console.log(`   - Enhanced accounts count: ${enhancedData.length}`);
      
      const healthyCount = enhancedData.filter(acc => acc.status === 'healthy').length;
      console.log(`   - Healthy accounts: ${healthyCount}/${enhancedData.length}`);
    } catch (error) {
      console.log('   - Could not read enhanced accounts file');
    }
  } else {
    console.log('\n⚠️ No account files found');
  }
}

// Main function
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'status';

  switch (command) {
    case 'migrate':
      await migrateToEnhancedAccounts();
      break;
    case 'rollback':
      await rollbackMigration();
      break;
    case 'status':
      await checkMigrationStatus();
      break;
    default:
      console.log('Usage: node migrate-to-enhanced-accounts.js [migrate|rollback|status]');
      console.log('');
      console.log('Commands:');
      console.log('  migrate  - Migrate basic accounts to enhanced format');
      console.log('  rollback - Rollback to basic accounts format');
      console.log('  status   - Check current migration status');
      process.exit(1);
  }
}

main().catch(console.error);
