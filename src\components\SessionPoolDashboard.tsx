'use client';

import React, { useState, useEffect } from 'react';
import { 
  Activity, 
  Users, 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  RotateCcw,
  TrendingUp,
  Shield,
  Zap,
  Eye,
  RefreshCw
} from 'lucide-react';

interface SessionPoolMetrics {
  activeSessions: number;
  healthyAccounts: number;
  totalAccounts: number;
  averageSessionAge: number;
  requestsPerHour: number;
  rotationsToday: number;
  errorRate: number;
}

interface ActiveSession {
  username: string;
  requestCount: number;
  isHealthy: boolean;
  ageMinutes: number;
  lastUsedMinutes: number;
}

interface AccountSummary {
  total: number;
  healthy: number;
  inCooldown: number;
  withErrors: number;
}

interface SessionPoolStatus {
  initialized: boolean;
  activeSessions: ActiveSession[];
  accountSummary: AccountSummary;
  metrics: SessionPoolMetrics;
}

export function SessionPoolDashboard() {
  const [status, setStatus] = useState<SessionPoolStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  const fetchStatus = async () => {
    try {
      const response = await fetch('/api/session-pool/status');
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      setStatus(data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch status');
      console.error('Failed to fetch session pool status:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatus();
    
    if (autoRefresh) {
      const interval = setInterval(fetchStatus, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const formatDuration = (minutes: number): string => {
    if (minutes < 60) {
      return `${minutes}m`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  const getHealthColor = (isHealthy: boolean): string => {
    return isHealthy ? 'text-green-500' : 'text-red-500';
  };

  const getUsageColor = (requestCount: number, maxRequests: number = 5): string => {
    const ratio = requestCount / maxRequests;
    if (ratio < 0.5) return 'text-green-500';
    if (ratio < 0.8) return 'text-yellow-500';
    return 'text-red-500';
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-center">
          <RefreshCw className="h-6 w-6 animate-spin text-blue-500 mr-2" />
          <span className="text-gray-600 dark:text-gray-300">Loading session pool status...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div className="flex items-center text-red-500 mb-4">
          <AlertTriangle className="h-6 w-6 mr-2" />
          <span className="font-semibold">Error Loading Session Pool Status</span>
        </div>
        <p className="text-gray-600 dark:text-gray-300 mb-4">{error}</p>
        <button
          onClick={fetchStatus}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  if (!status) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div className="text-center text-gray-500 dark:text-gray-400">
          No session pool data available
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Activity className="h-6 w-6 text-blue-500" />
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Session Pool Dashboard
          </h2>
        </div>
        
        <div className="flex items-center space-x-3">
          <label className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="rounded"
            />
            <span>Auto-refresh</span>
          </label>
          
          <button
            onClick={fetchStatus}
            className="p-2 text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
            title="Refresh now"
          >
            <RefreshCw className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Status Indicator */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div className="flex items-center space-x-3">
          {status.initialized ? (
            <CheckCircle className="h-6 w-6 text-green-500" />
          ) : (
            <XCircle className="h-6 w-6 text-red-500" />
          )}
          <span className="text-lg font-semibold text-gray-900 dark:text-white">
            Session Pool {status.initialized ? 'Active' : 'Inactive'}
          </span>
        </div>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Active Sessions */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Active Sessions</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {status.metrics.activeSessions}
              </p>
            </div>
            <Zap className="h-8 w-8 text-blue-500" />
          </div>
          <p className="text-xs text-gray-500 mt-2">Target: 3 sessions</p>
        </div>

        {/* Healthy Accounts */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Healthy Accounts</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {status.accountSummary.healthy}/{status.accountSummary.total}
              </p>
            </div>
            <Shield className="h-8 w-8 text-green-500" />
          </div>
          <p className="text-xs text-gray-500 mt-2">
            {((status.accountSummary.healthy / status.accountSummary.total) * 100).toFixed(1)}% healthy
          </p>
        </div>

        {/* Requests Per Hour */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Requests/Hour</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {status.metrics.requestsPerHour}
              </p>
            </div>
            <TrendingUp className="h-8 w-8 text-purple-500" />
          </div>
          <p className="text-xs text-gray-500 mt-2">Current rate</p>
        </div>

        {/* Rotations Today */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Rotations Today</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {status.metrics.rotationsToday}
              </p>
            </div>
            <RotateCcw className="h-8 w-8 text-orange-500" />
          </div>
          <p className="text-xs text-gray-500 mt-2">Session rotations</p>
        </div>
      </div>

      {/* Active Sessions Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <Eye className="h-5 w-5 mr-2" />
            Active Sessions
          </h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Account
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Usage
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Age
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Last Used
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {status.activeSessions.map((session, index) => (
                <tr key={session.username} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {session.username}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {session.isHealthy ? (
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-500 mr-2" />
                      )}
                      <span className={`text-sm ${getHealthColor(session.isHealthy)}`}>
                        {session.isHealthy ? 'Healthy' : 'Unhealthy'}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className={`text-sm font-medium ${getUsageColor(session.requestCount)}`}>
                        {session.requestCount}/5
                      </span>
                      <div className="ml-2 w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            session.requestCount < 3 ? 'bg-green-500' :
                            session.requestCount < 4 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${(session.requestCount / 5) * 100}%` }}
                        />
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                    {formatDuration(session.ageMinutes)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                    {formatDuration(session.lastUsedMinutes)} ago
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          
          {status.activeSessions.length === 0 && (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              No active sessions
            </div>
          )}
        </div>
      </div>

      {/* Account Summary */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <Users className="h-5 w-5 mr-2" />
          Account Summary
        </h3>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {status.accountSummary.total}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Total</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-green-500">
              {status.accountSummary.healthy}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Healthy</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-500">
              {status.accountSummary.inCooldown}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">In Cooldown</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-red-500">
              {status.accountSummary.withErrors}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">With Errors</div>
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <Activity className="h-5 w-5 mr-2" />
          Performance Metrics
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">Average Session Age</div>
            <div className="text-xl font-semibold text-gray-900 dark:text-white">
              {formatDuration(Math.floor(status.metrics.averageSessionAge / (1000 * 60)))}
            </div>
          </div>
          
          <div>
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">Error Rate</div>
            <div className="text-xl font-semibold text-gray-900 dark:text-white">
              {(status.metrics.errorRate * 100).toFixed(1)}%
            </div>
          </div>
          
          <div>
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">Health Score</div>
            <div className="text-xl font-semibold text-gray-900 dark:text-white">
              {((status.accountSummary.healthy / status.accountSummary.total) * 100).toFixed(0)}%
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
