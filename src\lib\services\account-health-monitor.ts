// Account Health Monitoring System
// Sophisticated monitoring with predictive health scoring and automatic rotation

import { SmartAccount<PERSON>ool<PERSON>anager, ManagedAccount } from './smart-account-pool-manager';
import axios from 'axios';

export interface HealthMetrics {
  responseTime: number[];
  successRate: number;
  errorTypes: Record<string, number>;
  requestVolume: number;
  consecutiveFailures: number;
  consecutiveSuccesses: number;
  lastActiveTime: number;
  accountAge: number; // days since first use
  stabilityScore: number; // 0-100, based on consistency
  reliabilityScore: number; // 0-100, based on success patterns
  riskScore: number; // 0-100, higher = more risky
}

export interface HealthAlert {
  accountId: string;
  username: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  type: 'performance' | 'security' | 'availability' | 'prediction';
  message: string;
  timestamp: number;
  metrics: Partial<HealthMetrics>;
  recommendedActions: string[];
}

export interface PredictiveModel {
  name: string;
  description: string;
  predict: (account: ManagedAccount, metrics: HealthMetrics) => {
    riskScore: number;
    timeToFailure?: number; // estimated days until failure
    confidence: number; // 0-100
    factors: string[];
  };
}

export interface HealthMonitorConfig {
  checkInterval: number; // ms between health checks
  alertThresholds: {
    lowHealth: number;
    mediumRisk: number;
    highRisk: number;
    criticalRisk: number;
  };
  predictiveModels: string[]; // names of models to use
  autoRotation: {
    enabled: boolean;
    healthThreshold: number;
    riskThreshold: number;
    cooldownPeriod: number; // ms before re-enabling account
  };
  validation: {
    enabled: boolean;
    interval: number; // ms between validation checks
    endpoints: string[]; // test endpoints
    timeout: number;
  };
}

export class AccountHealthMonitor {
  private accountPool: SmartAccountPoolManager;
  private config: HealthMonitorConfig;
  private healthMetrics: Map<string, HealthMetrics> = new Map();
  private alerts: HealthAlert[] = [];
  private predictiveModels: Map<string, PredictiveModel> = new Map();
  private monitoringInterval: NodeJS.Timeout | null = null;
  private validationInterval: NodeJS.Timeout | null = null;
  private readonly maxAlerts = 1000;

  constructor(accountPool: SmartAccountPoolManager, config?: Partial<HealthMonitorConfig>) {
    this.accountPool = accountPool;
    this.config = {
      checkInterval: 60000, // 1 minute
      alertThresholds: {
        lowHealth: 70,
        mediumRisk: 40,
        highRisk: 60,
        criticalRisk: 80
      },
      predictiveModels: ['failure_prediction', 'ban_risk_assessment', 'performance_degradation'],
      autoRotation: {
        enabled: true,
        healthThreshold: 30,
        riskThreshold: 75,
        cooldownPeriod: 3600000 // 1 hour
      },
      validation: {
        enabled: true,
        interval: 300000, // 5 minutes
        endpoints: [
          'https://www.instagram.com/api/v1/users/web_profile_info/?username=instagram',
          'https://www.instagram.com/instagram/'
        ],
        timeout: 10000
      },
      ...config
    };

    this.initializePredictiveModels();
    this.initializeHealthMetrics();
    this.startMonitoring();
  }

  private initializePredictiveModels() {
    // Failure Prediction Model
    this.predictiveModels.set('failure_prediction', {
      name: 'Failure Prediction',
      description: 'Predicts likelihood of account failure based on error patterns',
      predict: (account, metrics) => {
        let riskScore = 0;
        const factors: string[] = [];

        // Consecutive failures
        if (metrics.consecutiveFailures > 3) {
          riskScore += 30;
          factors.push(`${metrics.consecutiveFailures} consecutive failures`);
        }

        // Error type analysis
        const criticalErrors = ['login_required', 'challenge_required', 'rate_limit_exceeded'];
        for (const errorType of criticalErrors) {
          if (metrics.errorTypes[errorType] > 0) {
            riskScore += 20;
            factors.push(`${errorType} errors detected`);
          }
        }

        // Success rate decline
        if (metrics.successRate < 0.8) {
          riskScore += 25;
          factors.push(`Low success rate: ${(metrics.successRate * 100).toFixed(1)}%`);
        }

        // Response time degradation
        const avgResponseTime = metrics.responseTime.reduce((a, b) => a + b, 0) / metrics.responseTime.length;
        if (avgResponseTime > 5000) {
          riskScore += 15;
          factors.push(`Slow response times: ${avgResponseTime.toFixed(0)}ms`);
        }

        const timeToFailure = riskScore > 50 ? Math.max(1, 30 - (riskScore - 50)) : undefined;
        const confidence = Math.min(95, 60 + (factors.length * 10));

        return {
          riskScore: Math.min(100, riskScore),
          timeToFailure,
          confidence,
          factors
        };
      }
    });

    // Ban Risk Assessment Model
    this.predictiveModels.set('ban_risk_assessment', {
      name: 'Ban Risk Assessment',
      description: 'Assesses risk of account being banned or suspended',
      predict: (account, metrics) => {
        let riskScore = 0;
        const factors: string[] = [];

        // High request volume
        if (metrics.requestVolume > 50) {
          riskScore += 20;
          factors.push(`High request volume: ${metrics.requestVolume}`);
        }

        // Suspicious error patterns
        const suspiciousErrors = ['challenge_required', 'checkpoint_required', 'suspicious_activity'];
        for (const errorType of suspiciousErrors) {
          if (metrics.errorTypes[errorType] > 0) {
            riskScore += 35;
            factors.push(`Suspicious activity: ${errorType}`);
          }
        }

        // Account age factor (newer accounts are riskier)
        if (metrics.accountAge < 30) {
          riskScore += 15;
          factors.push(`New account (${metrics.accountAge} days old)`);
        }

        // Stability issues
        if (metrics.stabilityScore < 50) {
          riskScore += 20;
          factors.push(`Low stability score: ${metrics.stabilityScore}`);
        }

        const confidence = Math.min(90, 50 + (factors.length * 15));

        return {
          riskScore: Math.min(100, riskScore),
          confidence,
          factors
        };
      }
    });

    // Performance Degradation Model
    this.predictiveModels.set('performance_degradation', {
      name: 'Performance Degradation',
      description: 'Detects gradual performance degradation patterns',
      predict: (account, metrics) => {
        let riskScore = 0;
        const factors: string[] = [];

        // Response time trend
        if (metrics.responseTime.length >= 10) {
          const recent = metrics.responseTime.slice(-5);
          const older = metrics.responseTime.slice(-10, -5);
          const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
          const olderAvg = older.reduce((a, b) => a + b, 0) / older.length;

          if (recentAvg > olderAvg * 1.5) {
            riskScore += 25;
            factors.push('Response time degradation detected');
          }
        }

        // Reliability decline
        if (metrics.reliabilityScore < 70) {
          riskScore += 20;
          factors.push(`Low reliability: ${metrics.reliabilityScore}`);
        }

        // Increasing error frequency
        const totalErrors = Object.values(metrics.errorTypes).reduce((a, b) => a + b, 0);
        if (totalErrors > metrics.requestVolume * 0.1) {
          riskScore += 15;
          factors.push('Increasing error frequency');
        }

        const confidence = Math.min(85, 40 + (factors.length * 12));

        return {
          riskScore: Math.min(100, riskScore),
          confidence,
          factors
        };
      }
    });
  }

  private initializeHealthMetrics() {
    for (const account of this.accountPool.getAllAccounts()) {
      this.createHealthMetrics(account);
    }
  }

  private createHealthMetrics(account: ManagedAccount): HealthMetrics {
    const metrics: HealthMetrics = {
      responseTime: [],
      successRate: 1.0,
      errorTypes: {},
      requestVolume: 0,
      consecutiveFailures: account.health.consecutiveFailures,
      consecutiveSuccesses: account.health.consecutiveSuccesses,
      lastActiveTime: account.usage.lastUsed,
      accountAge: Math.floor((Date.now() - account.createdAt) / (24 * 60 * 60 * 1000)),
      stabilityScore: 100,
      reliabilityScore: 100,
      riskScore: 0
    };

    this.healthMetrics.set(account.id, metrics);
    return metrics;
  }

  private startMonitoring() {
    this.monitoringInterval = setInterval(() => {
      this.performHealthCheck();
    }, this.config.checkInterval);

    if (this.config.validation.enabled) {
      this.validationInterval = setInterval(() => {
        this.performValidationChecks();
      }, this.config.validation.interval);
    }

    console.log('🏥 Account Health Monitor started');
  }

  private async performHealthCheck() {
    console.log('🔍 Performing comprehensive health check...');

    for (const account of this.accountPool.getAllAccounts()) {
      await this.checkAccountHealth(account);
    }

    // Run predictive analysis
    this.runPredictiveAnalysis();

    // Check for auto-rotation candidates
    if (this.config.autoRotation.enabled) {
      await this.checkAutoRotation();
    }

    console.log('✅ Health check completed');
  }

  private async checkAccountHealth(account: ManagedAccount) {
    let metrics = this.healthMetrics.get(account.id);
    if (!metrics) {
      metrics = this.createHealthMetrics(account);
    }

    // Update basic metrics from account data
    metrics.consecutiveFailures = account.health.consecutiveFailures;
    metrics.consecutiveSuccesses = account.health.consecutiveSuccesses;
    metrics.lastActiveTime = account.usage.lastUsed;
    metrics.requestVolume = account.usage.dailyRequests;

    // Calculate success rate
    if (account.health.totalRequests > 0) {
      metrics.successRate = account.health.successfulRequests / account.health.totalRequests;
    }

    // Update error types from request history
    metrics.errorTypes = {};
    for (const request of account.usage.requestHistory) {
      if (!request.success && request.errorType) {
        metrics.errorTypes[request.errorType] = (metrics.errorTypes[request.errorType] || 0) + 1;
      }
    }

    // Calculate stability score (consistency over time)
    metrics.stabilityScore = this.calculateStabilityScore(account, metrics);

    // Calculate reliability score (predictable performance)
    metrics.reliabilityScore = this.calculateReliabilityScore(account, metrics);

    // Update response times
    if (account.health.averageResponseTime > 0) {
      metrics.responseTime.push(account.health.averageResponseTime);
      if (metrics.responseTime.length > 20) {
        metrics.responseTime = metrics.responseTime.slice(-20); // Keep last 20 measurements
      }
    }

    // Generate alerts if needed
    this.checkForAlerts(account, metrics);

    this.healthMetrics.set(account.id, metrics);
  }

  private calculateStabilityScore(account: ManagedAccount, metrics: HealthMetrics): number {
    let score = 100;

    // Penalize for inconsistent performance
    if (metrics.responseTime.length > 5) {
      const variance = this.calculateVariance(metrics.responseTime);
      const mean = metrics.responseTime.reduce((a, b) => a + b, 0) / metrics.responseTime.length;
      const cv = Math.sqrt(variance) / mean; // Coefficient of variation
      
      if (cv > 0.5) score -= 20; // High variability
      else if (cv > 0.3) score -= 10; // Moderate variability
    }

    // Penalize for frequent status changes
    if (account.health.consecutiveFailures > 0 && account.health.consecutiveSuccesses > 0) {
      const instability = Math.min(account.health.consecutiveFailures, 5) * 5;
      score -= instability;
    }

    // Reward for consistent success
    if (account.health.consecutiveSuccesses > 10) {
      score += Math.min(account.health.consecutiveSuccesses - 10, 20);
    }

    return Math.max(0, Math.min(100, score));
  }

  private calculateReliabilityScore(account: ManagedAccount, metrics: HealthMetrics): number {
    let score = 100;

    // Base on success rate
    score = metrics.successRate * 100;

    // Adjust for error patterns
    const criticalErrors = ['login_required', 'challenge_required', 'account_suspended'];
    for (const errorType of criticalErrors) {
      if (metrics.errorTypes[errorType] > 0) {
        score -= metrics.errorTypes[errorType] * 10;
      }
    }

    // Adjust for response time consistency
    if (metrics.responseTime.length > 0) {
      const avgResponseTime = metrics.responseTime.reduce((a, b) => a + b, 0) / metrics.responseTime.length;
      if (avgResponseTime > 3000) score -= 10; // Slow responses
      if (avgResponseTime > 5000) score -= 20; // Very slow responses
    }

    return Math.max(0, Math.min(100, score));
  }

  private calculateVariance(numbers: number[]): number {
    const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
    const squaredDiffs = numbers.map(n => Math.pow(n - mean, 2));
    return squaredDiffs.reduce((a, b) => a + b, 0) / numbers.length;
  }

  private runPredictiveAnalysis() {
    for (const account of this.accountPool.getAllAccounts()) {
      const metrics = this.healthMetrics.get(account.id);
      if (!metrics) continue;

      let maxRiskScore = 0;
      const allFactors: string[] = [];

      // Run all enabled predictive models
      for (const modelName of this.config.predictiveModels) {
        const model = this.predictiveModels.get(modelName);
        if (!model) continue;

        const prediction = model.predict(account, metrics);
        maxRiskScore = Math.max(maxRiskScore, prediction.riskScore);
        allFactors.push(...prediction.factors);

        // Generate alert for high-risk predictions
        if (prediction.riskScore > this.config.alertThresholds.highRisk) {
          this.generateAlert(account, {
            severity: prediction.riskScore > this.config.alertThresholds.criticalRisk ? 'critical' : 'high',
            type: 'prediction',
            message: `${model.name}: High risk detected (${prediction.riskScore.toFixed(1)}%)`,
            metrics,
            recommendedActions: this.getRecommendedActions(prediction.factors)
          });
        }
      }

      // Update overall risk score
      metrics.riskScore = maxRiskScore;
    }
  }

  private async checkAutoRotation() {
    const candidatesForRotation = this.accountPool.getAllAccounts().filter(account => {
      const metrics = this.healthMetrics.get(account.id);
      if (!metrics) return false;

      return (
        account.health.score < this.config.autoRotation.healthThreshold ||
        metrics.riskScore > this.config.autoRotation.riskThreshold
      );
    });

    if (candidatesForRotation.length > 0) {
      console.log(`🔄 Found ${candidatesForRotation.length} accounts for auto-rotation`);

      for (const account of candidatesForRotation) {
        await this.rotateAccount(account);
      }
    }
  }

  private async rotateAccount(account: ManagedAccount) {
    console.log(`🔄 Rotating account: ${account.credentials.username}`);

    // Set account to maintenance mode
    account.status = 'maintenance';
    account.usage.cooldownUntil = Date.now() + this.config.autoRotation.cooldownPeriod;

    // Generate alert
    const metrics = this.healthMetrics.get(account.id);
    this.generateAlert(account, {
      severity: 'medium',
      type: 'availability',
      message: 'Account automatically rotated due to health/risk concerns',
      metrics,
      recommendedActions: ['Session refresh', 'Extended cooldown', 'Manual review']
    });

    // Reset some metrics
    if (metrics) {
      metrics.consecutiveFailures = 0;
      metrics.errorTypes = {};
    }
  }

  private async performValidationChecks() {
    console.log('🔍 Performing validation checks...');

    const sampleAccounts = this.accountPool.getAllAccounts()
      .filter(account => account.status === 'active')
      .slice(0, 5); // Test only 5 accounts to avoid detection

    for (const account of sampleAccounts) {
      await this.validateAccount(account);
    }
  }

  private async validateAccount(account: ManagedAccount) {
    try {
      const startTime = Date.now();
      
      // Test with a simple Instagram endpoint
      const response = await axios.get(this.config.validation.endpoints[0], {
        headers: {
          'User-Agent': account.session.userAgent,
          'Cookie': `sessionid=${account.session.sessionid}; csrftoken=${account.session.csrftoken}`,
          'X-CSRFToken': account.session.csrftoken
        },
        timeout: this.config.validation.timeout
      });

      const responseTime = Date.now() - startTime;
      
      if (response.status === 200) {
        // Update metrics
        const metrics = this.healthMetrics.get(account.id);
        if (metrics) {
          metrics.responseTime.push(responseTime);
          if (metrics.responseTime.length > 20) {
            metrics.responseTime = metrics.responseTime.slice(-20);
          }
        }

        // Mark as healthy if validation succeeds
        account.health.consecutiveSuccesses++;
        account.health.consecutiveFailures = 0;
      }

    } catch (error) {
      console.warn(`Validation failed for ${account.credentials.username}:`, error);
      
      // Update failure metrics
      account.health.consecutiveFailures++;
      account.health.consecutiveSuccesses = 0;

      const metrics = this.healthMetrics.get(account.id);
      if (metrics && error instanceof Error) {
        const errorType = this.categorizeError(error.message);
        metrics.errorTypes[errorType] = (metrics.errorTypes[errorType] || 0) + 1;
      }
    }
  }

  private categorizeError(errorMessage: string): string {
    if (errorMessage.includes('401') || errorMessage.includes('login')) return 'login_required';
    if (errorMessage.includes('429') || errorMessage.includes('rate')) return 'rate_limit_exceeded';
    if (errorMessage.includes('challenge')) return 'challenge_required';
    if (errorMessage.includes('timeout')) return 'timeout';
    if (errorMessage.includes('network')) return 'network_error';
    return 'unknown_error';
  }

  private checkForAlerts(account: ManagedAccount, metrics: HealthMetrics) {
    // Health score alerts
    if (account.health.score < this.config.alertThresholds.lowHealth) {
      this.generateAlert(account, {
        severity: account.health.score < 30 ? 'critical' : 'high',
        type: 'performance',
        message: `Low health score: ${account.health.score.toFixed(1)}`,
        metrics,
        recommendedActions: ['Session refresh', 'Reduce request frequency', 'Manual review']
      });
    }

    // Consecutive failure alerts
    if (metrics.consecutiveFailures > 5) {
      this.generateAlert(account, {
        severity: 'high',
        type: 'performance',
        message: `${metrics.consecutiveFailures} consecutive failures`,
        metrics,
        recommendedActions: ['Immediate rotation', 'Session validation', 'Error analysis']
      });
    }

    // Success rate alerts
    if (metrics.successRate < 0.7 && metrics.requestVolume > 5) {
      this.generateAlert(account, {
        severity: 'medium',
        type: 'performance',
        message: `Low success rate: ${(metrics.successRate * 100).toFixed(1)}%`,
        metrics,
        recommendedActions: ['Investigate errors', 'Reduce load', 'Session check']
      });
    }
  }

  private generateAlert(account: ManagedAccount, alertData: Partial<HealthAlert>) {
    const alert: HealthAlert = {
      accountId: account.id,
      username: account.credentials.username,
      severity: 'medium',
      type: 'performance',
      message: '',
      timestamp: Date.now(),
      metrics: {},
      recommendedActions: [],
      ...alertData
    };

    this.alerts.push(alert);

    // Keep alerts manageable
    if (this.alerts.length > this.maxAlerts) {
      this.alerts = this.alerts.slice(-this.maxAlerts);
    }

    console.log(`🚨 Alert generated: ${alert.severity} - ${alert.message} (${alert.username})`);
  }

  private getRecommendedActions(factors: string[]): string[] {
    const actions = new Set<string>();

    for (const factor of factors) {
      if (factor.includes('consecutive failures')) {
        actions.add('Immediate account rotation');
        actions.add('Session validation');
      }
      if (factor.includes('rate_limit') || factor.includes('429')) {
        actions.add('Extend cooldown periods');
        actions.add('Reduce request frequency');
      }
      if (factor.includes('login') || factor.includes('challenge')) {
        actions.add('Manual session refresh');
        actions.add('Account verification');
      }
      if (factor.includes('response time')) {
        actions.add('Network diagnostics');
        actions.add('Proxy rotation');
      }
    }

    return Array.from(actions);
  }

  /**
   * Get health statistics
   */
  getHealthStats() {
    const accounts = this.accountPool.getAllAccounts();
    const metrics = Array.from(this.healthMetrics.values());

    return {
      totalAccounts: accounts.length,
      healthyAccounts: accounts.filter(a => a.health.score >= this.config.alertThresholds.lowHealth).length,
      unhealthyAccounts: accounts.filter(a => a.health.score < this.config.alertThresholds.lowHealth).length,
      averageHealthScore: accounts.reduce((sum, a) => sum + a.health.score, 0) / accounts.length,
      averageRiskScore: metrics.reduce((sum, m) => sum + m.riskScore, 0) / metrics.length,
      totalAlerts: this.alerts.length,
      criticalAlerts: this.alerts.filter(a => a.severity === 'critical').length,
      recentAlerts: this.alerts.filter(a => Date.now() - a.timestamp < 24 * 60 * 60 * 1000).length,
      accountsInMaintenance: accounts.filter(a => a.status === 'maintenance').length,
      accountsRetired: accounts.filter(a => a.status === 'retired').length
    };
  }

  /**
   * Get recent alerts
   */
  getRecentAlerts(limit: number = 50): HealthAlert[] {
    return this.alerts
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }

  /**
   * Get account health details
   */
  getAccountHealth(accountId: string) {
    const account = this.accountPool.getAccount(accountId);
    const metrics = this.healthMetrics.get(accountId);
    
    if (!account || !metrics) return null;

    return {
      account: {
        id: account.id,
        username: account.credentials.username,
        status: account.status,
        healthScore: account.health.score
      },
      metrics,
      alerts: this.alerts.filter(a => a.accountId === accountId).slice(-10)
    };
  }

  /**
   * Cleanup
   */
  cleanup() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }
    
    if (this.validationInterval) {
      clearInterval(this.validationInterval);
    }

    console.log('🧹 Account Health Monitor cleaned up');
  }
}
