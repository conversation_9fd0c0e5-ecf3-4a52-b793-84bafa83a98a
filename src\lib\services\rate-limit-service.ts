/**
 * Comprehensive Rate Limiting Service
 * Enforces 3 requests per 24-hour period per unique user with advanced fraud detection
 */

import { NextRequest } from 'next/server';
import { RateLimitDatabase } from './rate-limit-database';
import { UserFingerprint } from '../utils/user-fingerprint';
import {
  RateLimitRecord,
  RateLimitResponse,
  RateLimitError,
  SuspiciousActivityError,
  UserTier,
  RateLimitConfig,
  GeoLocation
} from '../types/rate-limiting';

export class RateLimitService {
  private static instance: RateLimitService;
  private database: RateLimitDatabase;
  private config: RateLimitConfig | null = null;

  constructor() {
    this.database = RateLimitDatabase.getInstance();
  }

  public static getInstance(): RateLimitService {
    if (!RateLimitService.instance) {
      RateLimitService.instance = new RateLimitService();
    }
    return RateLimitService.instance;
  }

  /**
   * Initialize the service
   */
  public async initialize(): Promise<void> {
    await this.database.initialize();
    this.config = await this.database.getConfig();
    console.log('Rate limiting service initialized');
  }

  /**
   * Check if a request is allowed and update rate limiting data
   */
  public async checkRateLimit(
    fingerprint: UserFingerprint,
    request: NextRequest,
    endpoint: string = 'unknown'
  ): Promise<RateLimitResponse> {
    if (!this.config) {
      await this.initialize();
    }

    const startTime = Date.now();
    const ipAddress = this.getClientIP(request);
    const userAgent = request.headers.get('user-agent') || 'unknown';

    try {
      // Get or create rate limit record
      const record = await this.database.upsertRateLimitRecord(
        fingerprint.fingerprintId,
        fingerprint,
        ipAddress,
        endpoint,
        userAgent,
        true, // Assume success for now
        0 // Will be updated later
      );

      // Check if user is blocked
      if (record.isBlocked) {
        if (record.blockExpiresAt && Date.now() > record.blockExpiresAt) {
          // Block has expired, unblock user
          await this.database.unblockUser(fingerprint.fingerprintId);
        } else {
          throw new RateLimitError(
            record.blockReason || 'User is blocked',
            'USER_BLOCKED',
            undefined,
            record.blockExpiresAt
          );
        }
      }

      // Check risk score
      if (record.riskScore > this.config!.maxRiskScore) {
        await this.database.blockUser(
          fingerprint.fingerprintId,
          `High risk score: ${record.riskScore}`,
          24 * 60 * 60 * 1000 // 24 hours
        );
        
        throw new SuspiciousActivityError(
          'Suspicious activity detected',
          record.riskScore,
          record.suspiciousActivity
        );
      }

      // Get user's rate limit based on tier
      const dailyLimit = this.getDailyLimit(record.userTier, record.customLimits);
      
      // Check daily limit
      if (record.dailyRequestCount > dailyLimit) {
        const retryAfter = Math.ceil((record.windowResetAt - Date.now()) / 1000);
        
        throw new RateLimitError(
          'Daily rate limit exceeded',
          'DAILY_LIMIT_EXCEEDED',
          retryAfter,
          record.windowResetAt
        );
      }

      // Check burst limit
      const recentRequests = record.requestHistory.filter(
        h => Date.now() - h.timestamp < this.config!.burstWindow
      ).length;

      if (recentRequests > this.config!.burstLimit) {
        const retryAfter = Math.ceil(this.config!.burstWindow / 1000);
        
        throw new RateLimitError(
          'Burst rate limit exceeded',
          'BURST_LIMIT_EXCEEDED',
          retryAfter
        );
      }

      // Request is allowed
      const remaining = Math.max(0, dailyLimit - record.dailyRequestCount);
      
      return {
        allowed: true,
        remaining,
        resetTime: record.windowResetAt,
        dailyLimit,
        windowStart: record.windowStartAt,
        windowEnd: record.windowResetAt,
        userTier: record.userTier
      };

    } catch (error) {
      if (error instanceof RateLimitError || error instanceof SuspiciousActivityError) {
        throw error;
      }

      // Log unexpected errors
      console.error('Rate limiting error:', error);
      
      // Return a conservative response for unknown errors
      throw new RateLimitError(
        'Rate limiting service unavailable',
        'SERVICE_ERROR',
        60 // Retry after 1 minute
      );
    } finally {
      // Update response time in the record
      const responseTime = Date.now() - startTime;
      // Note: In a real implementation, you might want to update this separately
      // to avoid the complexity of updating the record again
    }
  }

  /**
   * Update request success status
   */
  public async updateRequestStatus(
    fingerprintId: string,
    success: boolean,
    responseTime: number
  ): Promise<void> {
    try {
      const record = await this.database.getRateLimitRecord(fingerprintId);
      if (record && record.requestHistory.length > 0) {
        // Update the most recent request
        record.requestHistory[0].success = success;
        record.requestHistory[0].responseTime = responseTime;
        record.updatedAt = Date.now();
        
        // Re-calculate risk score based on updated data
        // This would be done in the database service
      }
    } catch (error) {
      console.error('Failed to update request status:', error);
    }
  }

  /**
   * Get user's daily limit based on tier and custom limits
   */
  private getDailyLimit(userTier: UserTier, customLimits?: any): number {
    if (customLimits?.dailyLimit) {
      return customLimits.dailyLimit;
    }

    switch (userTier) {
      case UserTier.PREMIUM:
        return this.config!.premiumDailyLimit;
      case UserTier.ENTERPRISE:
        return this.config!.enterpriseDailyLimit;
      case UserTier.BLOCKED:
        return 0;
      case UserTier.FREE:
      default:
        return this.config!.defaultDailyLimit;
    }
  }

  /**
   * Extract client IP address from request
   */
  private getClientIP(request: NextRequest): string {
    // Check various headers for the real IP
    const headers = [
      'x-forwarded-for',
      'x-real-ip',
      'x-client-ip',
      'cf-connecting-ip', // Cloudflare
      'x-forwarded',
      'forwarded-for',
      'forwarded'
    ];

    for (const header of headers) {
      const value = request.headers.get(header);
      if (value) {
        // Take the first IP if there are multiple
        const ip = value.split(',')[0].trim();
        if (this.isValidIP(ip)) {
          return ip;
        }
      }
    }

    // Fallback to connection IP
    return request.ip || 'unknown';
  }

  /**
   * Validate IP address format
   */
  private isValidIP(ip: string): boolean {
    const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
    const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
    
    return ipv4Regex.test(ip) || ipv6Regex.test(ip);
  }

  /**
   * Upgrade user tier
   */
  public async upgradeUserTier(
    fingerprintId: string,
    newTier: UserTier,
    customLimits?: any
  ): Promise<void> {
    const record = await this.database.getRateLimitRecord(fingerprintId);
    if (record) {
      record.userTier = newTier;
      if (customLimits) {
        record.customLimits = customLimits;
      }
      record.updatedAt = Date.now();
      
      // Save the updated record
      // Note: This would need to be implemented in the database service
      console.log(`Upgraded user ${fingerprintId} to tier ${newTier}`);
    }
  }

  /**
   * Get rate limit status for a user
   */
  public async getRateLimitStatus(fingerprintId: string): Promise<RateLimitResponse | null> {
    const record = await this.database.getRateLimitRecord(fingerprintId);
    if (!record) {
      return null;
    }

    const dailyLimit = this.getDailyLimit(record.userTier, record.customLimits);
    const remaining = Math.max(0, dailyLimit - record.dailyRequestCount);

    return {
      allowed: !record.isBlocked && remaining > 0,
      remaining,
      resetTime: record.windowResetAt,
      dailyLimit,
      windowStart: record.windowStartAt,
      windowEnd: record.windowResetAt,
      userTier: record.userTier,
      error: record.isBlocked ? record.blockReason : undefined
    };
  }

  /**
   * Reset user's rate limit (admin function)
   */
  public async resetUserRateLimit(fingerprintId: string): Promise<void> {
    const record = await this.database.getRateLimitRecord(fingerprintId);
    if (record) {
      const now = Date.now();
      record.dailyRequestCount = 0;
      record.windowStartAt = now;
      record.windowResetAt = now + this.config!.defaultWindowDuration;
      record.isBlocked = false;
      record.blockReason = undefined;
      record.blockExpiresAt = undefined;
      record.updatedAt = now;
      
      console.log(`Reset rate limit for user ${fingerprintId}`);
    }
  }

  /**
   * Get service statistics
   */
  public async getStats() {
    return this.database.getStats();
  }

  /**
   * Update service configuration
   */
  public async updateConfig(newConfig: Partial<RateLimitConfig>): Promise<void> {
    await this.database.updateConfig(newConfig);
    this.config = await this.database.getConfig();
  }
}

// Singleton instance
let rateLimitServiceInstance: RateLimitService | null = null;

/**
 * Get the singleton instance of RateLimitService
 */
export function getRateLimitService(): RateLimitService {
  if (!rateLimitServiceInstance) {
    rateLimitServiceInstance = RateLimitService.getInstance();
  }
  return rateLimitServiceInstance;
}
